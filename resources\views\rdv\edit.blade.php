@extends('layout')

@section('content')
<div class="pcoded-main-container">
    <div class="pcoded-content">
        <!-- [ breadcrumb ] start -->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10">Modifier le Rendez-vous #{{ $rdv->id }}</h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}"><i class="feather icon-home"></i></a></li>
                            <li class="breadcrumb-item"><a href="{{ route('calendrier.index') }}">Calendrier</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('rdv.show', $rdv) }}">RDV #{{ $rdv->id }}</a></li>
                            <li class="breadcrumb-item"><a href="#!">Modifier</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!-- [ breadcrumb ] end -->

        <!-- [ Main Content ] start -->
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-edit mr-2"></i>Modifier le Rendez-vous</h5>
                    </div>
                    <div class="card-body">
                        <form id="editRdvForm" action="{{ route('rdv.update', $rdv) }}" method="POST">
                            @csrf
                            @method('PUT')
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="client_id">Client <span class="text-danger">*</span></label>
                                        <select class="form-control" id="client_id" name="client_id" required>
                                            <option value="">Sélectionner un client</option>
                                            @foreach($clients as $client)
                                                <option value="{{ $client->id }}" {{ $rdv->client_id == $client->id ? 'selected' : '' }}>
                                                    {{ $client->prenom }} {{ $client->nom }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="car_id">Véhicule <span class="text-danger">*</span></label>
                                        <select class="form-control" id="car_id" name="car_id" required>
                                            <option value="">Sélectionner un véhicule</option>
                                            @if($rdv->car)
                                                <option value="{{ $rdv->car->id }}" selected>
                                                    {{ $rdv->car->marque }} {{ $rdv->car->modele }} ({{ $rdv->car->immatriculation }})
                                                </option>
                                            @endif
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="jour_rdv">Date du RDV <span class="text-danger">*</span></label>
                                        <input type="date" class="form-control" id="jour_rdv" name="jour_rdv" 
                                               value="{{ $rdv->jour_rdv->format('Y-m-d') }}" required>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="heure_rdv">Heure du RDV <span class="text-danger">*</span></label>
                                        <select class="form-control" id="heure_rdv" name="heure_rdv" required>
                                            <option value="">Sélectionner une heure</option>
                                            <option value="{{ $rdv->heure_rdv->format('H:i') }}" selected>
                                                {{ $rdv->heure_rdv->format('H:i') }}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="type_rdv">Type de RDV <span class="text-danger">*</span></label>
                                        <select class="form-control" id="type_rdv" name="type_rdv" required>
                                            <option value="">Sélectionner un type</option>
                                            <option value="Réparation" {{ $rdv->type_rdv == 'Réparation' ? 'selected' : '' }}>Réparation</option>
                                            <option value="Entretien" {{ $rdv->type_rdv == 'Entretien' ? 'selected' : '' }}>Entretien</option>
                                            <option value="Diagnostic" {{ $rdv->type_rdv == 'Diagnostic' ? 'selected' : '' }}>Diagnostic</option>
                                            <option value="Contrôle technique" {{ $rdv->type_rdv == 'Contrôle technique' ? 'selected' : '' }}>Contrôle technique</option>
                                            <option value="Carrosserie" {{ $rdv->type_rdv == 'Carrosserie' ? 'selected' : '' }}>Carrosserie</option>
                                            <option value="Autre" {{ $rdv->type_rdv == 'Autre' ? 'selected' : '' }}>Autre</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="statut">Statut</label>
                                        <select class="form-control" id="statut" name="statut">
                                            <option value="en_attente" {{ $rdv->statut == 'en_attente' ? 'selected' : '' }}>En attente</option>
                                            <option value="confirme" {{ $rdv->statut == 'confirme' ? 'selected' : '' }}>Confirmé</option>
                                            <option value="en_cours" {{ $rdv->statut == 'en_cours' ? 'selected' : '' }}>En cours</option>
                                            <option value="termine" {{ $rdv->statut == 'termine' ? 'selected' : '' }}>Terminé</option>
                                            <option value="annule" {{ $rdv->statut == 'annule' ? 'selected' : '' }}>Annulé</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="priority">Priorité</label>
                                        <select class="form-control" id="priority" name="priority">
                                            <option value="">Sélectionner une priorité</option>
                                            <option value="1" {{ $rdv->priority == 1 ? 'selected' : '' }}>Très Haute</option>
                                            <option value="2" {{ $rdv->priority == 2 ? 'selected' : '' }}>Haute</option>
                                            <option value="3" {{ $rdv->priority == 3 ? 'selected' : '' }}>Normale</option>
                                            <option value="4" {{ $rdv->priority == 4 ? 'selected' : '' }}>Basse</option>
                                            <option value="5" {{ $rdv->priority == 5 ? 'selected' : '' }}>Très Basse</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="estimated_duration">Durée Estimée (heures)</label>
                                        <input type="number" class="form-control" id="estimated_duration" name="estimated_duration" 
                                               step="0.5" min="0" value="{{ $rdv->estimated_duration }}">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="cost_estimate">Devis (€)</label>
                                        <input type="number" class="form-control" id="cost_estimate" name="cost_estimate" 
                                               step="0.01" min="0" value="{{ $rdv->cost_estimate }}">
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="final_cost">Coût Final (€)</label>
                                        <input type="number" class="form-control" id="final_cost" name="final_cost" 
                                               step="0.01" min="0" value="{{ $rdv->final_cost }}">
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="description">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="3">{{ $rdv->description }}</textarea>
                            </div>

                            <div class="form-group">
                                <label for="notes">Notes</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3">{{ $rdv->notes }}</textarea>
                            </div>

                            <div class="form-group text-right">
                                <a href="{{ route('rdv.show', $rdv) }}" class="btn btn-secondary">
                                    <i class="fas fa-times mr-1"></i>Annuler
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save mr-1"></i>Enregistrer
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- [ Informations Actuelles ] start -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle mr-2"></i>Informations Actuelles</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td><strong>ID:</strong></td>
                                <td>#{{ $rdv->id }}</td>
                            </tr>
                            <tr>
                                <td><strong>Créé le:</strong></td>
                                <td>{{ $rdv->created_at->format('d/m/Y H:i') }}</td>
                            </tr>
                            <tr>
                                <td><strong>Modifié le:</strong></td>
                                <td>{{ $rdv->updated_at->format('d/m/Y H:i') }}</td>
                            </tr>
                            @if($rdv->createdBy)
                                <tr>
                                    <td><strong>Créé par:</strong></td>
                                    <td>{{ $rdv->createdBy->prenom }} {{ $rdv->createdBy->nom }}</td>
                                </tr>
                            @endif
                            @if($rdv->assignedTo)
                                <tr>
                                    <td><strong>Assigné à:</strong></td>
                                    <td>{{ $rdv->assignedTo->prenom }} {{ $rdv->assignedTo->nom }}</td>
                                </tr>
                            @endif
                            @if($rdv->currentStage)
                                <tr>
                                    <td><strong>Étape actuelle:</strong></td>
                                    <td>{{ $rdv->currentStage->name }}</td>
                                </tr>
                            @endif
                        </table>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-lightbulb mr-2"></i>Conseils</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success mr-2"></i>Vérifiez la disponibilité avant de changer la date</li>
                            <li><i class="fas fa-check text-success mr-2"></i>Informez le client des modifications</li>
                            <li><i class="fas fa-check text-success mr-2"></i>Mettez à jour le statut selon l'avancement</li>
                            <li><i class="fas fa-check text-success mr-2"></i>Ajoutez des notes pour le suivi</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!-- [ Main Content ] end -->
    </div>
</div>

<script>
$(document).ready(function() {
    // Charger les véhicules du client sélectionné
    $('#client_id').change(function() {
        const clientId = $(this).val();
        const carSelect = $('#car_id');
        
        carSelect.empty().append('<option value="">Sélectionner un véhicule</option>');
        
        if (clientId) {
            $.get(`/clients/${clientId}/cars`, function(cars) {
                cars.forEach(function(car) {
                    carSelect.append(`<option value="${car.id}">${car.marque} ${car.modele} (${car.immatriculation})</option>`);
                });
                
                // Resélectionner le véhicule actuel si disponible
                @if($rdv->car)
                    carSelect.val('{{ $rdv->car->id }}');
                @endif
            }).fail(function(xhr) {
                console.error('Erreur lors du chargement des véhicules:', xhr.responseText);
            });
        }
    });

    // Charger les créneaux disponibles pour la date sélectionnée
    $('#jour_rdv').change(function() {
        const date = $(this).val();
        const heureSelect = $('#heure_rdv');
        const currentHeure = '{{ $rdv->heure_rdv->format("H:i") }}';
        
        if (date) {
            $.get('/calendrier/slots', { date: date }, function(slots) {
                heureSelect.empty().append('<option value="">Sélectionner une heure</option>');
                
                // Ajouter l'heure actuelle en premier
                heureSelect.append(`<option value="${currentHeure}" selected>${currentHeure} (Actuel)</option>`);
                
                slots.forEach(function(slot) {
                    if (slot.time !== currentHeure) {
                        heureSelect.append(`<option value="${slot.time}">${slot.label}</option>`);
                    }
                });
            }).fail(function(xhr) {
                console.error('Erreur lors du chargement des créneaux:', xhr.responseText);
            });
        }
    });

    // Soumission du formulaire
    $('#editRdvForm').submit(function(e) {
        e.preventDefault();
        
        $.ajax({
            url: $(this).attr('action'),
            method: 'PUT',
            data: $(this).serialize(),
            success: function(response) {
                alert('Rendez-vous modifié avec succès');
                window.location.href = '{{ route("rdv.show", $rdv) }}';
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    const errors = xhr.responseJSON.errors;
                    let errorMessage = 'Erreurs de validation:\n';
                    Object.keys(errors).forEach(function(key) {
                        errorMessage += '- ' + errors[key][0] + '\n';
                    });
                    alert(errorMessage);
                } else {
                    alert('Erreur lors de la modification: ' + (xhr.responseJSON?.message || 'Erreur inconnue'));
                }
            }
        });
    });
});
</script>
@endsection
