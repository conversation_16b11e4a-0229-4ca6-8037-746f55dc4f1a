<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class RdvStage extends Model
{
    use HasFactory;

    protected $table = 'rdv_stages';
    protected $guarded = [];

    protected $fillable = [
        'rdv_id',
        'stage_id',
        'status',
        'start_date',
        'end_date',
        'user_id',
        'notes',
        'estimated_duration',
        'actual_duration'
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'estimated_duration' => 'integer',
        'actual_duration' => 'integer'
    ];

    /**
     * Relation avec le rendez-vous
     */
    public function rdv()
    {
        return $this->belongsTo(Rdv::class, 'rdv_id');
    }

    /**
     * Relation avec l'étape
     */
    public function stage()
    {
        return $this->belongsTo(Stage::class, 'stage_id');
    }

    /**
     * Relation avec l'utilisateur
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Obtenir la durée formatée
     */
    public function getFormattedDurationAttribute()
    {
        if (!$this->actual_duration) {
            return 'Non définie';
        }

        $hours = floor($this->actual_duration / 60);
        $minutes = $this->actual_duration % 60;

        if ($hours > 0) {
            return $hours . 'h ' . $minutes . 'min';
        }

        return $minutes . 'min';
    }

    /**
     * Obtenir le statut formaté
     */
    public function getFormattedStatusAttribute()
    {
        $statuses = [
            'en_attente' => 'En attente',
            'en_cours' => 'En cours',
            'termine' => 'Terminé',
            'suspendu' => 'Suspendu',
            'annule' => 'Annulé'
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * Scope pour les étapes en cours
     */
    public function scopeInProgress($query)
    {
        return $query->where('status', 'en_cours');
    }

    /**
     * Scope pour les étapes terminées
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'termine');
    }
}
