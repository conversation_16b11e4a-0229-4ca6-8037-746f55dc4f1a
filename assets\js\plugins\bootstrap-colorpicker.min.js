/*!
 * Bootstrap Colorpicker - Bootstrap Colorpicker is a modular color picker plugin for Bootstrap 4.
 * @package bootstrap-colorpicker
 * @version v3.4.0
 * @license MIT
 * @link https://itsjavi.com/bootstrap-colorpicker/
 * @link https://github.com/itsjavi/bootstrap-colorpicker.git
 */
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory(require('jquery')) :
  typeof define === 'function' && define.amd ? define('bootstrap-colorpicker', ['jquery'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global['bootstrap-colorpicker'] = factory(global.jQuery));
}(this, (function (jQuery) { 'use strict';

  function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

  var jQuery__default = /*#__PURE__*/_interopDefaultLegacy(jQuery);

  /**
   * Bootstrap Colorpicker - A modular color picker plugin for Bootstrap 4.
   */
  var Colorpicker = (function () {
    /**
     * Colorpicker constructor
     *
     * @param {Element|jQuery} element
     * @param {Object} options
     * @constructor
     */
    function Colorpicker(element, options) {
      this.element = jQuery__default['default'](element);
      this.options = jQuery__default['default'].extend(true, {}, defaults, options);
      this.colorHandler = new ColorHandler(this);
      this.sliderHandler = new SliderHandler(this);
      this.popupHandler = new PopupHandler(this);
      this.inputHandler = new InputHandler(this);
      this.pickerHandler = new PickerHandler(this);
      this.addonHandler = new AddonHandler(this);
      this.init();
      this.element.data('colorpicker', this);
    }

    /**
     * Initializes the plugin
     */
    Colorpicker.prototype.init = function () {
      // Initialize the color
      this.colorHandler.setColor(this.options.color || this.element.data('color'));

      // Initialize the picker
      this.pickerHandler.create();

      // Initialize the UI
      this.addonHandler.create();
      this.inputHandler.create();

      // Bind events
      this.bindEvents();

      // If the element is a component
      if (this.options.component) {
        this.element.addClass('colorpicker-component');
      }

      // Emit the create event
      this.emit('create');
    };

    /**
     * Binds the plugin events
     */
    Colorpicker.prototype.bindEvents = function () {
      var that = this;

      // Bind addon events
      this.element.on('click.colorpicker', '.colorpicker-input-addon', function (e) {
        that.pickerHandler.toggle(e);
      });

      // Bind input events
      this.element.on('keyup.colorpicker', 'input', function (e) {
        that.inputHandler.update(e);
      });

      // Bind change events
      this.element.on('change.colorpicker', 'input', function (e) {
        that.inputHandler.update(e);
      });
    };

    /**
     * Emits an event
     *
     * @param {String} event
     * @param {Object} data
     */
    Colorpicker.prototype.emit = function (event, data) {
      this.element.trigger({
        type: event + '.colorpicker',
        colorpicker: this,
        color: data || this.colorHandler.getColor()
      });
    };

    /**
     * Returns the color object
     *
     * @returns {Object}
     */
    Colorpicker.prototype.getColor = function () {
      return this.colorHandler.getColor();
    };

    /**
     * Sets the color
     *
     * @param {String|Object} color
     */
    Colorpicker.prototype.setColor = function (color) {
      this.colorHandler.setColor(color);
      this.update();
    };

    /**
     * Updates the UI
     */
    Colorpicker.prototype.update = function () {
      this.pickerHandler.update();
      this.addonHandler.update();
      this.inputHandler.update();
      this.emit('update');
    };

    /**
     * Destroys the plugin
     */
    Colorpicker.prototype.destroy = function () {
      this.pickerHandler.destroy();
      this.addonHandler.destroy();
      this.inputHandler.destroy();
      this.element.removeData('colorpicker');
      this.element.off('.colorpicker');
      this.emit('destroy');
    };

    return Colorpicker;
  })();

  // Register the plugin
  jQuery__default['default'].fn.colorpicker = function (option) {
    var args = Array.prototype.slice.call(arguments, 1);
    var isSingleElement = this.length === 1;
    var returnValue = null;

    this.each(function () {
      var $this = jQuery__default['default'](this);
      var data = $this.data('colorpicker');
      var options = typeof option === 'object' ? option : {};

      if (!data) {
        data = new Colorpicker(this, options);
        $this.data('colorpicker', data);
      }

      if (typeof option === 'string') {
        if (typeof data[option] === 'function') {
          returnValue = data[option].apply(data, args);
        } else {
          returnValue = data[option];
        }
      } else {
        returnValue = $this;
      }

      if (isSingleElement) {
        return false;
      }
    });

    return isSingleElement ? returnValue : this;
  };

  // Define the defaults
  var defaults = {
    color: null,
    format: 'hex',
    container: false,
    component: true,
    input: 'input',
    addon: '.colorpicker-input-addon',
    fallbackColor: '#000000',
    useAlpha: true
  };

  return Colorpicker;

})));
