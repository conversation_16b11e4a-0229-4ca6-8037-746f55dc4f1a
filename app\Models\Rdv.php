<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Rdv extends Model
{
    use HasFactory;

    protected $table = 'rdv';
    protected $guarded = [];

    protected $fillable = [
        'car_id',
        'client_id',
        'jour_rdv',
        'heure_rdv',
        'type_rdv',
        'description',
        'statut',
        'current_stage_id',
        'priority',
        'estimated_duration',
        'actual_duration',
        'cost_estimate',
        'final_cost',
        'notes',
        'created_by',
        'assigned_to'
    ];

    protected $casts = [
        'jour_rdv' => 'date',
        'heure_rdv' => 'datetime',
        'priority' => 'integer',
        'estimated_duration' => 'integer',
        'actual_duration' => 'integer',
        'cost_estimate' => 'decimal:2',
        'final_cost' => 'decimal:2'
    ];

    /**
     * Relation avec la voiture
     */
    public function car()
    {
        return $this->belongsTo(Car::class, 'car_id');
    }

    /**
     * Relation avec le client
     */
    public function client()
    {
        return $this->belongsTo(Client::class, 'client_id');
    }

    /**
     * Relation avec l'étape actuelle
     */
    public function currentStage()
    {
        return $this->belongsTo(Stage::class, 'current_stage_id');
    }

    /**
     * Relation avec les étapes du RDV
     */
    public function rdvStages()
    {
        return $this->hasMany(RdvStage::class, 'rdv_id');
    }

    /**
     * Relation avec les photos des étapes
     */
    public function stagePhotos()
    {
        return $this->hasMany(StagePhoto::class, 'rdv_id');
    }

    /**
     * Relation avec les évaluations de dommages
     */
    public function damageAssessments()
    {
        return $this->hasMany(DamageAssessment::class, 'rdv_id');
    }

    /**
     * Relation avec le dernier statut d'étape
     */
    public function latestStageStatus()
    {
        return $this->hasOne(RdvStage::class, 'rdv_id')->latest();
    }

    /**
     * Relation avec l'utilisateur créateur
     */
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relation avec l'utilisateur assigné
     */
    public function assignedTo()
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Scope pour les RDV confirmés
     */
    public function scopeConfirmed($query)
    {
        return $query->where('statut', 'confirme');
    }

    /**
     * Scope pour les RDV en cours
     */
    public function scopeInProgress($query)
    {
        return $query->where('statut', 'en_cours');
    }

    /**
     * Scope pour les RDV terminés
     */
    public function scopeCompleted($query)
    {
        return $query->where('statut', 'termine');
    }

    /**
     * Obtenir le statut formaté
     */
    public function getFormattedStatusAttribute()
    {
        $statuses = [
            'en_attente' => 'En attente',
            'confirme' => 'Confirmé',
            'en_cours' => 'En cours',
            'termine' => 'Terminé',
            'annule' => 'Annulé'
        ];

        return $statuses[$this->statut] ?? $this->statut;
    }
}