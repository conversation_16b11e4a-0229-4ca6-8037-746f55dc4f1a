<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = [
            [
                'id' => 1,
                'prenom' => 'Admin',
                'nom' => 'System',
                'email' => '<EMAIL>',
                'telephone' => '0123456789',
                'password' => Hash::make('admin123'),
                'role' => 'admin',
                'active' => true,
                'permissions' => json_encode([
                    'all' => true,
                    'notifications' => [
                        'email_rdv' => true,
                        'email_stage' => true,
                        'email_rappel' => true,
                        'sms_rdv' => false,
                        'sms_urgent' => true
                    ]
                ]),
                'theme_preference' => 'system',
                'last_login' => now(),
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 2,
                'prenom' => 'Jean',
                'nom' => 'Dupont',
                'email' => '<EMAIL>',
                'telephone' => '0123456790',
                'password' => Hash::make('tech123'),
                'role' => 'technicien',
                'active' => true,
                'permissions' => json_encode([
                    'workshop' => true,
                    'photos' => true,
                    'notifications' => [
                        'email_rdv' => true,
                        'email_stage' => true,
                        'email_rappel' => false,
                        'sms_rdv' => true,
                        'sms_urgent' => true
                    ]
                ]),
                'theme_preference' => 'light',
                'last_login' => now()->subHours(2),
                'created_at' => now()->subDays(30),
                'updated_at' => now()
            ],
            [
                'id' => 3,
                'prenom' => 'Marie',
                'nom' => 'Martin',
                'email' => '<EMAIL>',
                'telephone' => '0123456791',
                'password' => Hash::make('tech123'),
                'role' => 'technicien',
                'active' => true,
                'permissions' => json_encode([
                    'workshop' => true,
                    'photos' => true,
                    'quality_control' => true,
                    'notifications' => [
                        'email_rdv' => true,
                        'email_stage' => true,
                        'email_rappel' => true,
                        'sms_rdv' => false,
                        'sms_urgent' => true
                    ]
                ]),
                'theme_preference' => 'dark',
                'last_login' => now()->subHours(1),
                'created_at' => now()->subDays(25),
                'updated_at' => now()
            ]
        ];

        DB::table('users')->insert($users);
    }
}
