
/* Variables CSS pour une cohérence des couleurs et facilité de maintenance */
:root {
    --primary-color: #4361ee;
    --primary-light: #4895ef;
    --primary-dark: #6f42c1;
    --secondary-color: #4cc9f0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --info-color: #2196f3;
    --dark-color: #212529;
    --light-color: #f8f9fa;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
    --border-radius: 10px;
    --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    --transition: all 0.3s ease;

    /* Valeurs RGB pour les couleurs (pour les transparences) */
    --primary-color-rgb: 67, 97, 238;
    --primary-dark-rgb: 111, 66, 193;
    --success-color-rgb: 76, 175, 80;
    --warning-color-rgb: 255, 152, 0;
    --danger-color-rgb: 244, 67, 54;
    --info-color-rgb: 33, 150, 243;
}

/* Styles généraux améliorés */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.btn {
    border-radius: 8px;
    padding: 8px 16px;
    font-weight: 500;
    transition: var(--transition);
}

.btn-primary {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
}

.btn-outline-primary.active {
    background: var(--primary-color);
    color: white;
}

/* En-tête du calendrier */
.calendar-header {
    background: white;
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: var(--box-shadow);
}

/* Navigation entre les semaines */
.week-navigation {
    margin-bottom: 15px;
    background-color: var(--gray-50);
    border-radius: var(--border-radius);
    padding: 8px;
    border: 1px solid var(--gray-200);
}

.week-title {
    font-size: 0.95rem;
    font-weight: 600;
    color: var(--gray-700);
}

.week-navigation .btn {
    font-size: 0.8rem;
    padding: 4px 10px;
    height: 28px;
    line-height: 1.2;
    border-radius: 4px;
    box-shadow: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Style spécifique pour la navigation en mode liste */
#list-view:not([style*="display: none"]) ~ .week-navigation {
    background-color: var(--gray-100);
    margin-top: -15px;
    margin-bottom: 15px;
    position: sticky;
    top: 0;
    z-index: 100;
}
    /* Styles pour améliorer les modals */
    .modal-dialog-scrollable .modal-content {
        max-height: 90vh;
        overflow-y: auto;
    }

    .modal-dialog-scrollable .modal-body {
        overflow-y: auto;
        padding-bottom: 20px;
    }

    /* Assurer que le modal est bien visible sur les petits écrans */
    @media (max-width: 576px) {
        .modal-dialog {
            margin: 0.5rem;
            max-width: calc(100% - 1rem);
        }

        .modal-dialog-scrollable .modal-content {
            max-height: 85vh;
        }
    }

/* Styles pour les écrans moyens et grands */
@media (min-width: 768px) {
    .week-navigation {
        padding: 10px;
    }

    .week-title {
        font-size: 1rem;
        margin-bottom: 8px;
    }

    .week-navigation .btn {
        font-size: 0.85rem;
        padding: 5px 12px;
        height: 34px;
        min-width: 40px;
    }
}

/* Styles spécifiques pour le bouton Liste sur les petits écrans */
@media (max-width: 767px) {
    #list-view-btn {
        padding: 5px 2px;
        font-size: 0.95rem;
        border-radius: 8px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;
        width: auto;
        min-width: 120px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    #list-view-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    #list-view-btn i {
        font-size: 1rem;
    }

    /* Ajuster le groupe de boutons quand il n'y a qu'un seul bouton */
    .view-toggle {
        display: flex;
        justify-content: center;
        width: 100%;
    }

    /* Autres styles pour les petits écrans */
    .week-navigation {
        padding: 6px;
        margin-bottom: 10px;
    }

    .week-title {
        font-size: 0.9rem;
        margin-bottom: 5px;
    }

    .week-navigation .btn {
        font-size: 0.75rem;
        padding: 3px 8px;
        height: 28px;
        min-width: 32px;
    }
}

/* Déjà défini plus haut */

/* Grille du calendrier */
.calendar-grid {
    display: grid;
    grid-template-columns: 80px repeat(7, minmax(120px, 1fr));
    gap: 1px;
    background: white;
    border-radius: var(--border-radius);
    overflow-x: auto;
    box-shadow: var(--box-shadow);
    min-width: 100%;
}

.day-header {
    background: var(--gray-100);
    padding: 15px 5px;
    text-align: center;
    font-weight: 600;
    font-size: 14px;
    color: var(--dark-color);
    border-bottom: 2px solid var(--gray-200);
    position: sticky;
    top: 0;
    z-index: 10;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.day-header .day-number {
    font-size: 18px;
    font-weight: 700;
    margin: 3px 0;
    color: var(--primary-color);
}

.day-header small {
    font-size: 11px;
    opacity: 0.7;
}

.time-slot {
    background: var(--gray-100);
    padding: 10px;
    text-align: center;
    color: var(--gray-700);
    font-size: 12px;
    border-right: 1px solid var(--gray-200);
    position: sticky;
    left: 0;
    z-index: 5;
}

.cell {
    background: white;
    min-height: 90px;
    border: 1px solid var(--gray-200);
    font-size: 12px;
    position: relative;
    transition: var(--transition);
}

.cell:hover {
    background: var(--gray-100);
}

/* Boîtes d'événements */
.event-box {
    margin: 5px;
    padding: 10px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 12px;
    transition: var(--transition);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
}

.event-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.event-header {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.badge-circle {
    background: rgba(255, 255, 255, 0.9);
    width: 30px;
    height: 30px;
    line-height: 30px;
    font-size: 14px;
    border-radius: 50%;
    text-align: center;
    color: var(--dark-color);
    font-weight: bold;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
}

.event-immat {
    margin-left: 8px;
    font-weight: 600;
    font-size: 11px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.event-content {
    font-weight: 500;
}

.event-model {
    font-weight: 600;
    margin-bottom: 3px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.event-client {
    opacity: 0.9;
    font-size: 11px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Couleurs des événements avec dégradés améliorés */
.blue {
    background: linear-gradient(135deg, #4361ee, #4895ef);
}

.green {
    background: linear-gradient(135deg, #4caf50, #2e7d32);
}

.teal {
    background: linear-gradient(135deg, #009688, #00796b);
}

.orange {
    background: linear-gradient(135deg, #ff9800, #f57c00);
}

.red {
    background: linear-gradient(135deg, #f44336, #d32f2f);
}

.purple {
    background: linear-gradient(135deg, #9c27b0, #7b1fa2);
}

/* Couleurs des événements selon le statut */
.en_attente {
    background-color: var(--warning-color);
    border-left: 4px solid rgba(0, 0, 0, 0.2);
    color: #000000;
    font-weight: 600;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.3);
}

.en_cours {
    background-color: var(--primary-dark);
    border-left: 4px solid rgba(0, 0, 0, 0.2);
    color: #ffffff;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.confirme {
    background-color: var(--success-color);
    border-left: 4px solid rgba(0, 0, 0, 0.2);
    color: #ffffff;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.annule {
    background-color: var(--danger-color);
    border-left: 4px solid rgba(0, 0, 0, 0.2);
    color: #ffffff;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.termine {
    background-color: var(--info-color);
    border-left: 4px solid rgba(0, 0, 0, 0.2);
    color: #ffffff;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Styles pour les badges dans les event-box */
.en_attente .badge-circle {
    color: var(--warning-color);
    background-color: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.en_cours .badge-circle {
    color: var(--primary-dark);
    background-color: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.confirme .badge-circle {
    color: var(--success-color);
    background-color: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.annule .badge-circle {
    color: var(--danger-color);
    background-color: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.termine .badge-circle {
    color: var(--info-color);
    background-color: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Vue liste */
.list-view-table {
    display: none;
}

.list-view-table .table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: none;
    background-color: transparent;
}

.list-view-table .table thead th {
    background: transparent;
    border-bottom: 1px solid var(--gray-300);
    color: var(--gray-700);
    font-weight: 600;
    padding: 12px 15px;
}

.list-view-table .table tbody tr {
    background-color: transparent;
}

.list-view-table .table tbody tr:hover {
    background-color: rgba(var(--primary-color-rgb), 0.05);
}

/* Badges de statut */
.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    color: white;
    display: inline-block;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-en_attente {
    background-color: var(--warning-color);
}

.status-en_cours {
    background-color: var(--primary-dark);
}

.status-confirme {
    background-color: var(--success-color);
}

.status-annule {
    background-color: var(--danger-color);
}

.status-termine {
    background-color: var(--info-color);
}

/* Couleurs pour les informations de véhicule selon le statut */
.vehicle-info {
    transition: var(--transition);
    border-left: 3px solid transparent;
    padding-left: 8px;
    margin-left: -8px;
}
.vehicle-info-en_attente {
    border-left-color: var(--warning-color);
    color: var(--warning-color) !important;
}
.vehicle-info-en_cours {
    border-left-color: var(--primary-dark);
    color: var(--secondary-color) !important;
}
.vehicle-info-confirme {
    border-left-color: var(--success-color);
    color: var(--success-color) !important;
}
.vehicle-info-annule {
    border-left-color: var(--danger-color);
    color: var(--danger-color) !important;
}
.vehicle-info-termine {
    border-left-color: var(--info-color);
    color: var(--info-color) !important;
}

/* Formulaires */
.form-control,
.form-select {
    border-radius: 8px;
    border: 1px solid var(--gray-300);
    padding: 10px 15px;
    transition: var(--transition);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-light);
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

.form-group label {
    font-weight: 500;
    margin-bottom: 5px;
    color: var(--gray-700);
}

/* Modals */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.modal-header {
    border-bottom: 1px solid var(--gray-200);
    background-color: var(--gray-100);
    border-top-left-radius: var(--border-radius);
    border-top-right-radius: var(--border-radius);
}

.modal-footer {
    border-top: 1px solid var(--gray-200);
}

/* Styles pour les cartes de rendez-vous (vue mobile) */
.appointment-card {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.appointment-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Styles améliorés pour les cartes de rendez-vous */
.appointment-card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s, box-shadow 0.2s;
    overflow: hidden;
    margin-bottom: 20px;
}

.appointment-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.appointment-card .card-header {
    background-color: var(--gray-100);
    border-bottom: 1px solid var(--gray-200);
    padding: 15px;
    font-weight: 600;
}

.appointment-card .status-badge {
    font-size: 0.8rem;
    padding: 5px 10px;
    border-radius: 20px;
}

.appointment-card .card-body {
    padding: 20px;
    background-color: white;
}

.appointment-card .card-subtitle {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--dark-color);
}

.appointment-card .vehicle-info {
    display: inline-block;
    padding: 4px 10px;
    border-radius: 4px;
    margin-bottom: 10px;
    font-weight: 500;
}

/* Style spécifique pour le badge-circle dans les cartes */
.appointment-card .badge-circle {
    width: 40px;
    height: 40px;
    line-height: 40px;
    font-size: 16px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    border: none;
    box-shadow: 0 2px 5px rgba(var(--primary-color-rgb), 0.3);
    font-weight: 600;
    margin-right: 12px;
}

.appointment-card .vehicle-info-en_attente {
    background-color: rgba(var(--warning-color-rgb), 0.1);
    color: var(--warning-color);
}

.appointment-card .vehicle-info-en_cours {
    background-color: rgba(var(--primary-dark-rgb), 0.1);
    color: var(--secondary-color);
}

.appointment-card .vehicle-info-confirme {
    background-color: rgba(var(--success-color-rgb), 0.1);
    color: var(--success-color);
}

.appointment-card .vehicle-info-annule {
    background-color: rgba(var(--danger-color-rgb), 0.1);
    color: var(--danger-color);
}

.appointment-card .vehicle-info-termine {
    background-color: rgba(var(--info-color-rgb), 0.1);
    color: var(--info-color);
}

.appointment-card .card-footer {
    background-color: var(--gray-100);
    border-top: 1px solid var(--gray-200);
    padding: 12px 15px;
}

.appointment-card .card-footer .btn {
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: 500;
    transition: all 0.2s;
}

.appointment-card .card-footer .btn:hover {
    transform: translateY(-2px);
}

/* Styles pour les cartes mobiles */
.mobile-cards {
    display: none;
}

@media (max-width: 992px) {
    .mobile-cards {
        display: block;
    }

    /* Cacher la table responsive sur les appareils mobiles */
    .table-responsive {
        display: none;
    }
}

/* S'assurer qu'il n'y a pas de duplication sur les écrans moyens */
@media (min-width: 768px) and (max-width: 992px) {
    .mobile-cards {
        display: block;
    }
    .table-responsive {
        display: none;
    }
}

/* Media queries pour la responsivité */
@media (max-width: 1200px) {
    .calendar-grid {
        grid-template-columns: 70px repeat(7, minmax(100px, 1fr));
    }
}

@media (max-width: 992px) {
    .week-navigation {
        flex-direction: column;
        align-items: stretch;
    }

    .week-navigation>div {
        margin: 10px 0;
        display: flex;
        justify-content: center;
    }

    .week-title {
        order: -1;
    }

    .nav-btn-group {
        display: flex;
        justify-content: center;
        gap: 10px;
    }
}

@media (max-width: 768px) {
    .calendar-grid {
        grid-template-columns: 60px repeat(7, minmax(80px, 1fr));
    }

    .day-header {
        padding: 10px 5px;
        font-size: 12px;
    }

    .cell {
        min-height: 70px;
    }

    .event-box {
        padding: 5px;
        margin: 3px;
    }

    .badge-circle {
        width: 25px;
        height: 25px;
        line-height: 25px;
        font-size: 12px;
        margin-bottom: 5px;
    }

    /* Styles pour la table responsive */
    .table-responsive table {
        width: 100%;
    }

    .table-responsive td[data-label] {
        position: relative;
    }

    .table-responsive td[data-label]::before {
        content: attr(data-label);
        font-weight: 600;
        color: var(--gray-700);
        display: none;
    }

    /* Styles pour les tables sur tablettes */
    @media (max-width: 992px) {
        .table-responsive table,
        .table-responsive thead,
        .table-responsive tbody,
        .table-responsive tr {
            display: block;
            width: 100%;
        }

        .table-responsive thead tr {
            position: absolute;
            top: -9999px;
            left: -9999px;
        }

        .table-responsive tr {
            margin-bottom: 15px;
            border: 1px solid var(--gray-300);
            border-radius: var(--border-radius);
            background-color: white;
        }

        .table-responsive td {
            display: block;
            border: none;
            border-bottom: 1px solid var(--gray-200);
            position: relative;
            padding-left: 50%;
            text-align: right;
            min-height: 45px;
        }

        .table-responsive td:last-child {
            border-bottom: none;
        }

        .table-responsive td::before {
            content: attr(data-label);
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            width: 45%;
            padding-right: 10px;
            white-space: nowrap;
            font-weight: 600;
            color: var(--gray-700);
            text-align: left;
            display: block;
        }

        .table-responsive td.text-end {
            text-align: center !important;
            padding-left: 15px;
        }

        .table-responsive td.text-end::before {
            display: none;
        }
    }
}

/* Styles pour s'assurer que le bouton d'ajout de rendez-vous est toujours visible */
.card-header {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
}

.card-header-right {
    margin-left: auto;
}

/* Style du bouton d'ajout de rendez-vous */
.card-header-right .btn-primary {
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.card-header-right .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Styles pour les écrans moyens */
@media (min-width: 576px) and (max-width: 991px) {
    .card-header-right .btn {
        font-size: 0.9rem;
        padding: 6px 14px;
    }
}

@media (max-width: 576px) {
    .calendar-header {
        padding: 15px 10px;
    }

    .card-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .card-header-right {
        margin-left: 0;
        margin-top: 8px;
        width: auto;
    }

    .card-header-right .btn {
        font-size: 0.8rem;
        padding: 5px 10px;
        border-radius: 6px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        height: auto;
    }

    /* Styles pour les très petits écrans */
    @media (max-width: 360px) {
        .card-header-right .btn {
            font-size: 0.75rem;
            padding: 4px 8px;
        }
    }

    .calendar-grid {
        grid-template-columns: 50px repeat(3, minmax(70px, 1fr));
    }

    /* Afficher seulement 3 jours à la fois sur mobile */
    .calendar-grid .day-header:nth-child(n+5),
    .calendar-grid .cell:nth-child(n+5) {
        display: none;
    }

    .week-title {
        font-size: 16px;
    }

    .btn {
        padding: 6px 12px;
        font-size: 14px;
    }

    /* Ajuster les boutons dans les tables responsives */
    .table-responsive .btn-group {
        display: flex;
        justify-content: center;
        width: 100%;
    }

    .table-responsive .btn-sm {
        padding: 8px 12px;
        margin: 0 5px;
    }

    /* Ajuster la navigation entre les semaines sur mobile */
    #list-view:not([style*="display: none"]) ~ .week-navigation {
        padding: 6px 8px;
        margin-bottom: 10px;
    }

    #list-view:not([style*="display: none"]) ~ .week-navigation .btn {
        font-size: 0.75rem;
        padding: 3px 6px;
        height: 24px;
    }

    #list-view:not([style*="display: none"]) ~ .week-navigation .week-title {
        font-size: 0.85rem;
        max-width: 120px;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    #list-view:not([style*="display: none"]) ~ .week-navigation .d-none {
        display: none !important;
    }
}
