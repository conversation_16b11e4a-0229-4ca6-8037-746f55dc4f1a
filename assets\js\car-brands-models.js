/**
 * Liste des marques de voitures et leurs modèles
 * Données des marques et modèles de voitures les plus populaires
 */
const carBrandsAndModels = {
    
        "Acura": ["MDX", "RDX", "TLX", "ILX", "NSX", "Integra", "ZDX"],
        "Alfa Romeo": ["Giulia", "Stelvio", "4C", "Giulietta", "MiTo", "Tonale"],
        "Aston Martin": ["DB11", "Vantage", "DBS", "Rapide", "Valkyrie", "DBX"],
        "Audi": ["A1", "A3", "A4", "A5", "A6", "A7", "A8", "Q2", "Q3", "Q4", "Q5", "Q7", "Q8", "TT", "R8", "e-tron GT"],
        "Bentley": ["Continental", "Bentayga", "Flying Spur", "<PERSON><PERSON>anne", "Azure"],
        "BMW": ["Série 1", "Série 2", "Série 3", "Série 4", "Série 5", "Série 6", "Série 7", "Série 8", "X1", "X2", "X3", "X4", "X5", "X6", "X7", "Z4", "i3", "i4", "i5", "i7", "i8", "iX", "XM"],
        "BYD": ["Han", "Tang", "Yuan Plus", "Seal", "Atto 3", "Dolphin"],
        "Buick": ["Encore", "Envision", "Enclave", "Regal", "LaCrosse", "Verano", "Lucerne"],
        "Cadillac": ["CT4", "CT5", "XT4", "XT5", "XT6", "Escalade", "Lyriq", "Celestiq"],
        "Chevrolet": ["Spark", "Sonic", "Cruze", "Malibu", "Impala", "Camaro", "Corvette", "Trax", "Trailblazer", "Equinox", "Blazer", "Traverse", "Tahoe", "Suburban", "Colorado", "Silverado", "Bolt"],
        "Chery": ["Tiggo 2", "Tiggo 4", "Tiggo 7", "Tiggo 8", "Arrizo 5", "Arrizo 6"],
        "Chrysler": ["300", "Pacifica", "Voyager", "Aspen"],
        "Citroën": ["C1", "C2", "C3", "C4", "C5", "Berlingo", "C3 Aircross", "C5 Aircross", "SpaceTourer", "DS3", "DS4", "DS5"],
        "Cupra": ["Born", "Formentor", "Leon", "Ateca"],
        "Dacia": ["Spring", "Sandero", "Logan", "Duster", "Lodgy", "Dokker", "Jogger"],
        "Daewoo": ["Lanos", "Nubira", "Leganza", "Matiz", "Kalos", "Tacuma"],
        "Daihatsu": ["Terios", "Sirion", "Materia", "Copen", "Charade", "Move", "Hijet"],
        "Dodge": ["Dart", "Challenger", "Charger", "Durango", "Journey", "Grand Caravan", "Hornet"],
        "Ferrari": ["488", "F8 Tributo", "812", "Roma", "SF90 Stradale", "Portofino", "296 GTB", "Daytona SP3"],
        "Fiat": ["500", "Panda", "Tipo", "500X", "500L", "124 Spider", "Punto", "Bravo", "Fiorino", "Doblo"],
        "Ford": ["Ka", "Fiesta", "Focus", "Fusion", "Mondeo", "Mustang", "Kuga", "Puma", "EcoSport", "Edge", "Explorer", "Expedition", "Ranger", "F-150", "Maverick", "Bronco", "Transit"],
        "Genesis": ["G70", "G80", "G90", "GV60", "GV70", "GV80"],
        "Geely": ["Coolray", "Emgrand", "Azkarra", "Okavango", "Geometry C"],
        "GMC": ["Terrain", "Acadia", "Yukon", "Canyon", "Sierra", "Hummer EV"],
        "Great Wall": ["Poer", "Wingle", "Haval H6", "Haval Jolion", "Haval Dargo"],
        "Honda": ["Fit", "Jazz", "City", "Civic", "Accord", "HR-V", "CR-V", "Pilot", "Odyssey", "Ridgeline", "Insight", "Prelude"],
        "Hummer": ["H1", "H2", "H3", "EV Pickup", "EV SUV"],
        "Hyundai": ["i10", "i20", "i30", "Ioniq", "Ioniq 5", "Kona", "Tucson", "Santa Fe", "Palisade", "Veloster", "Elantra", "Sonata", "Accent", "Bayon"],
        "Infiniti": ["Q30", "Q50", "Q60", "Q70", "QX30", "QX50", "QX55", "QX60", "QX70", "QX80"],
        "Isuzu": ["D-Max", "MU-X", "Rodeo", "Trooper"],
        "Jaguar": ["XE", "XF", "XJ", "F-Type", "E-Pace", "F-Pace", "I-Pace"],
        "Jeep": ["Renegade", "Compass", "Cherokee", "Grand Cherokee", "Wrangler", "Gladiator", "Commander"],
        "Kia": ["Picanto", "Rio", "Ceed", "Stonic", "Niro", "Sportage", "Sorento", "Stinger", "Soul", "Optima", "Seltos", "Carnival", "EV6"],
        "Koenigsegg": ["Regera", "Jesko", "Gemera", "CC850"],
        "Lamborghini": ["Huracán", "Aventador", "Urus", "Revuelto"],
        "Lancia": ["Ypsilon", "Delta", "Thema"],
        "Land Rover": ["Defender", "Discovery", "Discovery Sport", "Range Rover", "Range Rover Evoque", "Range Rover Sport", "Range Rover Velar"],
        "Lexus": ["CT", "IS", "ES", "GS", "LS", "RC", "LC", "UX", "NX", "RX", "GX", "LX"],
        "Lincoln": ["MKZ", "Continental", "Corsair", "Nautilus", "Aviator", "Navigator", "MKC", "MKT"],
        "Lotus": ["Elise", "Exige", "Evora", "Evija", "Emira"],
        "Lucid": ["Air", "Gravity"],
        "Maserati": ["Ghibli", "Quattroporte", "Levante", "GranTurismo", "MC20", "Grecale"],
        "Maybach": ["S-Class", "GLS"],
        "Mazda": ["2", "3", "6", "MX-5", "CX-3", "CX-30", "CX-5", "CX-50", "CX-60", "CX-9", "CX-90"],
        "McLaren": ["540C", "570S", "570GT", "600LT", "720S", "750S", "765LT", "GT", "Artura", "Senna", "P1", "Speedtail"],
        "Mercedes-Benz": ["Classe A", "Classe B", "Classe C", "Classe E", "Classe S", "CLA", "CLS", "GLA", "GLB", "GLC", "GLE", "GLS", "Classe G", "EQC", "EQE", "EQS", "EQA", "EQB", "Vito", "Sprinter"],
        "Mini": ["One", "Cooper", "Clubman", "Countryman", "Paceman", "Convertible", "Electric"],
        "Mitsubishi": ["Space Star", "Lancer", "ASX", "Eclipse Cross", "Outlander", "Pajero", "L200", "Xpander"],
        "Nissan": ["Micra", "Note", "Juke", "Qashqai", "X-Trail", "Murano", "Pathfinder", "Leaf", "370Z", "GT-R", "Navara", "Ariya"],
        "Opel": ["Adam", "Karl", "Corsa", "Astra", "Insignia", "Crossland", "Grandland", "Mokka", "Combo", "Zafira", "Vivaro"],
        "Peugeot": ["108", "208", "308", "508", "2008", "3008", "5008", "Rifter", "Traveller", "Expert"],
        "Polestar": ["1", "2", "3", "4"],
        "Porsche": ["718 Boxster", "718 Cayman", "911", "Taycan", "Panamera", "Macan", "Cayenne"],
        "Proton": ["Saga", "Persona", "Preve", "X70", "X50"],
        "RAM": ["1500", "2500", "3500", "ProMaster", "700"],
        "Renault": ["Twingo", "Clio", "Captur", "Mégane", "Kadjar", "Austral", "Scénic", "Talisman", "Espace", "Koleos", "Arkana", "Kangoo", "Trafic", "Master", "Zoe"],
        "Rivian": ["R1T", "R1S"],
        "Rolls-Royce": ["Ghost", "Wraith", "Dawn", "Phantom", "Cullinan", "Spectre"],
        "Saab": ["9-2X", "9-3", "9-4X", "9-5", "9-7X"],
        "Seat": ["Mii", "Ibiza", "Arona", "Leon", "Ateca", "Tarraco", "Alhambra", "Toledo"],
        "Skoda": ["Citigo", "Fabia", "Scala", "Octavia", "Superb", "Kamiq", "Karoq", "Kodiaq", "Enyaq"],
        "Smart": ["Fortwo", "Forfour", "EQ Fortwo", "Smart #1", "Smart #3"],
        "SsangYong": ["Tivoli", "Korando", "Rexton", "Musso", "Torres"],
        "Subaru": ["Impreza", "Legacy", "Outback", "Forester", "XV", "BRZ", "WRX", "Ascent", "Crosstrek"],
        "Suzuki": ["Alto", "Ignis", "Swift", "Baleno", "Vitara", "S-Cross", "Jimny", "Celerio", "Ertiga"],
        "Tata": ["Nexon", "Harrier", "Safari", "Tiago", "Tigor", "Punch"],
        "Tesla": ["Model 3", "Model S", "Model X", "Model Y", "Cybertruck", "Roadster", "Semi"],
        "Toyota": ["Aygo", "Yaris", "Yaris Cross", "Corolla", "Camry", "Avensis", "C-HR", "RAV4", "Highlander", "Land Cruiser", "Hilux", "Tacoma", "Tundra", "Supra", "Prius", "Mirai", "Sienna", "Proace"],
        "Volkswagen": ["up!", "Polo", "Golf", "T-Cross", "T-Roc", "Tiguan", "Touareg", "Touran", "Sharan", "Caddy", "Transporter", "Multivan", "Passat", "Arteon", "ID.3", "ID.4", "ID.5", "ID.Buzz"],
        "Volvo": ["S40", "S60", "S90", "V40", "V60", "V90", "XC40", "XC60", "XC90", "EX30", "EX90"]
    }
    
/**
 * Initialise les listes déroulantes de marques et modèles
 */
function initCarBrandsAndModels() {
    const marqueSelect = document.getElementById('marque');
    const modeleSelect = document.getElementById('modele');

    // Vider les listes
    marqueSelect.innerHTML = '<option value="">Sélectionnez une marque</option>';
    modeleSelect.innerHTML = '<option value="">Sélectionnez d\'abord une marque</option>';

    // Remplir la liste des marques
    Object.keys(carBrandsAndModels).sort().forEach(brand => {
        const option = document.createElement('option');
        option.value = brand;
        option.textContent = brand;
        marqueSelect.appendChild(option);
    });

    // Événement de changement de marque
    marqueSelect.addEventListener('change', function() {
        const selectedBrand = this.value;

        // Vider la liste des modèles
        modeleSelect.innerHTML = '';

        if (selectedBrand) {
            // Activer la liste des modèles
            modeleSelect.disabled = false;

            // Ajouter l'option par défaut
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = 'Sélectionnez un modèle';
            modeleSelect.appendChild(defaultOption);

            // Remplir la liste des modèles pour la marque sélectionnée
            carBrandsAndModels[selectedBrand].sort().forEach(model => {
                const option = document.createElement('option');
                option.value = model;
                option.textContent = model;
                modeleSelect.appendChild(option);
            });
        } else {
            // Désactiver la liste des modèles si aucune marque n'est sélectionnée
            modeleSelect.disabled = true;

            // Ajouter l'option par défaut
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = 'Sélectionnez d\'abord une marque';
            modeleSelect.appendChild(defaultOption);
        }
    });
}

// Initialiser les listes déroulantes lorsque le DOM est chargé
document.addEventListener('DOMContentLoaded', function() {
    initCarBrandsAndModels();

    // Gérer la sélection d'un véhicule existant
    const clientCarsSelect = document.getElementById('client_cars');
    if (clientCarsSelect) {
        clientCarsSelect.addEventListener('change', function() {
            if (this.value) {
                const selectedOption = this.options[this.selectedIndex];
                const marque = selectedOption.getAttribute('data-marque');
                const modele = selectedOption.getAttribute('data-modele');

                // Mettre à jour les champs marque et modèle
                const marqueSelect = document.getElementById('marque');
                const modeleSelect = document.getElementById('modele');

                // Trouver et sélectionner la marque
                for (let i = 0; i < marqueSelect.options.length; i++) {
                    if (marqueSelect.options[i].value === marque) {
                        marqueSelect.selectedIndex = i;
                        // Déclencher l'événement change pour charger les modèles
                        marqueSelect.dispatchEvent(new Event('change'));
                        break;
                    }
                }

                // Attendre que les modèles soient chargés, puis sélectionner le modèle
                setTimeout(() => {
                    let modeleFound = false;

                    // Chercher le modèle exact
                    for (let i = 0; i < modeleSelect.options.length; i++) {
                        if (modeleSelect.options[i].value === modele) {
                            modeleSelect.selectedIndex = i;
                            modeleFound = true;
                            break;
                        }
                    }

                    // Si le modèle exact n'est pas trouvé, l'ajouter
                    if (!modeleFound) {
                        const option = document.createElement('option');
                        option.value = modele;
                        option.textContent = modele;
                        modeleSelect.appendChild(option);
                        option.selected = true;
                    }
                }, 100);
            }
        });
    }
});
