@extends('layout');
@section('content')
<script src="https://cdn.jsdelivr.net/npm/apexcharts@3.44.0"></script>
<link rel="stylesheet" href="assets/css/index.css">

<!-- [ Main Content ] start -->
<div class="pcoded-main-container">
    <div class="pcoded-content">
        <!-- [ breadcrumb ] start -->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10">Activité</h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="index.html"><i class="fas fa-gauge-high"></i></a></li>
                            <li class="breadcrumb-item"><a href="#!">Activité</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!-- [ breadcrumb ] end -->
        <!-- [ Main Content ] start -->
         <!-- Première carte -->
         <div class="row">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Missions actives</h5><br>
                        <label class="text-muted">par étape mission</label>
                        <div class="card-header-right">
                            <div class="btn-group card-option">
                                <button type="button" class="btn dropdown-toggle btn-icon" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="feather icon-more-horizontal"></i>
                                </button>
                                <ul class="list-unstyled card-option dropdown-menu dropdown-menu-right">
                                    <li class="dropdown-item full-card"><a href="#!"><span><i class="feather icon-maximize"></i> maximize</span><span style="display:none"><i class="feather icon-minimize"></i> Restore</span></a></li>
                                    <li class="dropdown-item minimize-card"><a href="#!"><span><i class="feather icon-minus"></i> collapse</span><span style="display:none"><i class="feather icon-plus"></i> expand</span></a></li>
                                    <li class="dropdown-item reload-card"><a href="#!"><i class="feather icon-refresh-cw"></i> reload</a></li>
                                    <li class="dropdown-item close-card"><a href="#!"><i class="feather icon-trash"></i> remove</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div style="display: flex; align-items: center;">
                        <div class="col-md-4">
                            <div id="pie-chart-2"></div>
                            <div class="custom-total" id="custom-total">0</div>
                        </div>
                        <div class="col-md-8">
                            <div id="bars-container" class="bar-container"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Deuxième carte -->
        <div class="row">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Rendez-vous</h5> <br>
                        <label class="text-muted">par étape mission</label>
                        <div class="card-header-right">
                            <div class="btn-group card-option">
                                <button type="button" class="btn dropdown-toggle btn-icon" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="feather icon-more-horizontal"></i>
                                </button>
                                <ul class="list-unstyled card-option dropdown-menu dropdown-menu-right">
                                    <li class="dropdown-item full-card"><a href="#!"><span><i class="feather icon-maximize"></i> maximize</span><span style="display:none"><i class="feather icon-minimize"></i> Restore</span></a></li>
                                    <li class="dropdown-item minimize-card"><a href="#!"><span><i class="feather icon-minus"></i> collapse</span><span style="display:none"><i class="feather icon-plus"></i> expand</span></a></li>
                                    <li class="dropdown-item reload-card"><a href="#!"><i class="feather icon-refresh-cw"></i> reload</a></li>
                                    <li class="dropdown-item close-card"><a href="#!"><i class="feather icon-trash"></i> remove</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div style="display: flex; align-items: center;">
                        <div class="col-md-4">
                            <div style="position: relative;">
                                <div id="donut-chart-1"></div>
                                <div class="custom-total" id="custom-total-1">0</div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="right-section" id="bars-container-1"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>


<script>
    // Fonction pour initialiser le graphique avec les données
    function initMissionChart(data) {
        var missionLabels = data.labels;
        var missionSeries = data.series;
        var missionColors = data.colors;
        var missionTotal = data.total;

        // Mettre à jour le total affiché
        document.getElementById('custom-total').innerText = missionTotal;

        // Initialiser le graphique
        var missionChart = new ApexCharts(document.querySelector("#pie-chart-2"), {
            chart: { height: "auto", type: 'donut' },
            series: missionSeries,
            labels: missionLabels,
            colors: missionColors,
            legend: { show: false },
            plotOptions: {
                pie: {
                    donut: {
                        size: '85%',
                        labels: {
                            show: true,
                            total: {
                                show: false,
                                showAlways: true,
                                label: '',
                                fontSize: '24px',
                                fontWeight: 800,
                                color: '#2c3e50',
                                formatter: function(w) {
                                    return w.globals.seriesTotals.reduce((a, b) => a + b, 0);
                                }
                            }
                        }
                    }
                }
            },
            dataLabels: { enabled: false },
            tooltip: { enabled: false }
        });
        missionChart.render();

        // Générer les barres HTML
        var barsHtml = '';
        for (var i = 0; i < missionSeries.length; i++) {
            var value = missionSeries[i];
            var label = missionLabels[i];
            var color = missionColors[i];
            var percentage = missionTotal > 0 ? (value / missionTotal) * 100 : 0;

            barsHtml += `
                <div class="bar-item">
                    <strong>${value}</strong> <span class="bar-label">${label}</span>
                    <div class="bar-line" style="background-color: ${color}; width: ${percentage}%;"></div>
                </div>
            `;
        }
        document.getElementById("bars-container").innerHTML = barsHtml;
    }

    // Fonction pour afficher un message d'erreur
    function showChartError(message) {
        document.getElementById('pie-chart-2').innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> ${message}
            </div>
        `;
        document.getElementById("bars-container").innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> ${message}
            </div>
        `;
        document.getElementById('custom-total').innerText = '0';
    }

    // Charger les données depuis le serveur
    fetch('{{ url('/rdv-etat') }}?t=' + new Date().getTime())
        .then(response => {
            if (!response.ok) {
                throw new Error('Erreur réseau: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            if (!data.success) {
                throw new Error(data.error || 'Erreur lors du chargement des données');
            }
            // Initialiser le graphique avec les données reçues
            initMissionChart(data);
        })
        .catch(error => {
            console.error('Erreur lors du chargement des statistiques:', error);
            showChartError('Impossible de charger les statistiques: ' + error.message);

            // Utiliser des données par défaut en cas d'erreur
            initMissionChart({
                labels: ['Demandes à traiter', 'Photos avant', 'Réparation Atelier', 'Facturation', 'Livraison'],
                series: [0, 0, 0, 0, 0],
                colors: ["#95a5a6", "#00acc1", "#9b59b6", "#f39c12", "#1abc9c"],
                total: 0
            });
        });
</script>

<script>
    // Fonction pour initialiser le graphique avec les données
    function initRdvChart(data) {
        const rdvLabels = data.labels;
        const rdvColors = data.colors;
        const weeks = data.weeks;
        const rdvSeries = data.series;
        const rdvTotal = data.total;

        // Mettre à jour le total affiché
        document.getElementById('custom-total-1').innerText = rdvTotal;

        // Initialiser le graphique
        var rdvChart = new ApexCharts(document.querySelector("#donut-chart-1"), {
            chart: { height: "auto", type: 'donut' },
            series: rdvSeries,
            labels: rdvLabels,
            colors: rdvColors,
            legend: { show: false },
            plotOptions: {
                pie: {
                    donut: {
                        size: '85%',
                        labels: {
                            show: false,
                            total: {
                                show: true,
                                label: '',
                                fontSize: '26px',
                                fontWeight: 800,
                                color: '#2c3e50',
                                formatter: () => rdvTotal
                            }
                        }
                    }
                }
            },
            dataLabels: { enabled: false },
            tooltip: { enabled: false }
        });
        rdvChart.render();

        // Générer les barres HTML
        let barsHtml1 = '';

        // Vérifier s'il y a des semaines à afficher
        if (weeks.length === 0) {
            barsHtml1 = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Aucune semaine avec des rendez-vous n'a été trouvée.
                </div>
            `;
        } else {
            // Trier les semaines par ordre chronologique
            weeks.sort((a, b) => {
                const dateA = new Date(a.from.split(' ')[0] + ' ' + a.from.split(' ')[1]);
                const dateB = new Date(b.from.split(' ')[0] + ' ' + b.from.split(' ')[1]);
                return dateA - dateB; // Ordre croissant (du passé vers le futur)
            });

            // Générer les blocs pour chaque semaine
            weeks.forEach(week => {
                const sum = week.values.reduce((a, b) => a + b, 0);
                const percentages = week.values.map(v => sum ? (v / sum * 100).toFixed(2) : 0);

                // Déterminer si c'est la semaine courante, passée ou future
                const isCurrent = week.is_current;
                const isPast = week.is_past;
                const isFuture = week.is_future;

                // Ajouter des classes CSS selon le type de semaine
                let weekBlockClass = "week-block";
                if (isCurrent) weekBlockClass += " current-week";
                if (isFuture) weekBlockClass += " future-week";
                if (isPast) weekBlockClass += " past-week";

                // Ajouter un indicateur visuel pour la semaine courante et future
                let timeIndicator = "";
                if (isCurrent) {
                    timeIndicator = `<span class="badge bg-primary">Semaine actuelle</span>`;
                } else if (isFuture) {
                    timeIndicator = `<span class="badge bg-info">À venir</span>`;
                }

                barsHtml1 += `
                    <div class="${weekBlockClass}">
                        <div class="calendar">
                            <div class="calendar-icon"></div>
                            <div class="calendar-content">
                                <div class="calendar-date">
                                    <span>${week.from}</span>
                                    <span>${week.to}</span>
                                    ${timeIndicator}
                                </div>
                            </div>
                        </div>
                        <div class="bar-block">
                            <div class="bar-title">${sum} Rendez-vous</div>
                            <div class="bar">
                                ${week.values.map((v, i) => `<div class="${['blue','green','cyan'][i]}" style="width: ${percentages[i]}%"></div>`).join('')}
                            </div>
                        </div>
                    </div>
                `;
            });
        }

        document.getElementById("bars-container-1").innerHTML = barsHtml1;
    }

    // Fonction pour afficher un message d'erreur
    function showRdvChartError(message) {
        document.getElementById('donut-chart-1').innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> ${message}
            </div>
        `;
        document.getElementById("bars-container-1").innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> ${message}
            </div>
        `;
        document.getElementById('custom-total-1').innerText = '0';
    }

    // Charger les données depuis le serveur
    fetch('{{ url('/rdv-stats') }}?t=' + new Date().getTime())
    .then(response => {
        if (!response.ok) {
            throw new Error('Erreur réseau: ' + response.status);
        }
        return response.json();
    })
    .then(data => {
        if (!data.success) {
            throw new Error(data.error || 'Erreur lors du chargement des données');
        }
        // Initialiser le graphique avec les données reçues
        initRdvChart(data);
    })
    .catch(error => {
        console.error('Erreur lors du chargement des statistiques:', error);
        showRdvChartError('Impossible de charger les statistiques: ' + error.message);

        // Utiliser des données par défaut en cas d'erreur
        initRdvChart({
            labels: ["En attente", "En cours", "Terminé"],
            colors: ["#3b82f6", "#65a30d", "#14b8a6"],
            weeks: [
                { from: '01 jan.', to: '07 jan.', values: [0, 0, 0] },
                { from: '08 jan.', to: '14 jan.', values: [0, 0, 0] }
            ],
            series: [0, 0, 0],
            total: 0
        });
    });

</script>
<script>
document.addEventListener("DOMContentLoaded", function () {
    document.querySelectorAll(".minimize-card").forEach(btn => {
        btn.addEventListener("click", function (e) {
            e.preventDefault();

            const card = btn.closest(".card");
            const contentElements = card.querySelectorAll(".col-md-4, .col-md-8, .card-body, .bar-container, .right-section");
            const spanIcons = btn.querySelectorAll("span");

            let isVisible = Array.from(contentElements).some(el => el.style.display !== "none");

            contentElements.forEach(el => {
                el.style.display = isVisible ? "none" : "";
            });

            // Toggle icon text
            if (isVisible) {
                spanIcons[1].style.display = "none";  // collapse icon
                spanIcons[0].style.display = "";      // expand icon
            } else {
                spanIcons[1].style.display = "";      // collapse icon
                spanIcons[0].style.display = "none";  // expand icon
            }
        });
    });
});
</script>


@endsection
