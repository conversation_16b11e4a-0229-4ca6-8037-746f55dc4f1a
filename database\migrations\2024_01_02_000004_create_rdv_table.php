<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('rdv', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('car_id');
            $table->unsignedInteger('client_id');
            $table->date('jour_rdv');
            $table->time('heure_rdv');
            $table->string('type_rdv', 100);
            $table->text('description')->nullable();
            $table->enum('statut', ['en_attente', 'confirme', 'en_cours', 'termine', 'annule'])->default('en_attente');
            $table->unsignedInteger('current_stage_id')->default(1);
            $table->integer('priority')->default(3); // 1-5
            $table->integer('estimated_duration')->nullable(); // en minutes
            $table->integer('actual_duration')->nullable(); // en minutes
            $table->decimal('cost_estimate', 10, 2)->nullable();
            $table->decimal('final_cost', 10, 2)->nullable();
            $table->text('notes')->nullable();
            $table->unsignedInteger('created_by')->nullable();
            $table->unsignedInteger('assigned_to')->nullable();
            $table->timestamps();
            
            $table->foreign('car_id')->references('id')->on('cars')->onDelete('cascade');
            $table->foreign('client_id')->references('id')->on('clients')->onDelete('cascade');
            $table->foreign('current_stage_id')->references('id')->on('stages');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('assigned_to')->references('id')->on('users')->onDelete('set null');
            
            $table->index(['jour_rdv', 'heure_rdv']);
            $table->index('statut');
            $table->index('current_stage_id');
            $table->index('priority');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rdv');
    }
};
