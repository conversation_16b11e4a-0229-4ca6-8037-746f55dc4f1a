/* Styles personnalisés pour la barre de progression des étapes */
.progress-container {
    width: 100%;
    margin: 20px auto;
    position: relative;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 10px;
}

.progressbar {
    counter-reset: step;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin: 0;
}

.progressbar li {
    list-style-type: none;
    width: 33.33%; /* Trois étapes par ligne */
    font-size: 12px;
    position: relative;
    text-align: center;
    text-transform: uppercase;
    color: #7d7d7d;
    margin-bottom: 30px;
    font-weight: 500;
    padding: 0 5px;
}

.progressbar li:before {
    width: 40px;
    height: 40px;
    content: counter(step);
    counter-increment: step;
    line-height: 38px;
    border: 2px solid #7d7d7d;
    display: block;
    text-align: center;
    margin: 0 auto 10px auto;
    border-radius: 50%;
    background-color: white;
    z-index: 2;
    position: relative;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    font-weight: bold;
}

.progressbar li:after {
    width: 100%;
    height: 2px;
    content: '';
    position: absolute;
    background-color: #7d7d7d;
    top: 20px;
    left: -50%;
    z-index: 1;
}

.progressbar li:first-child:after {
    content: none;
}

/* Étapes terminées */
.progressbar li.completed {
    color: #28a745;
}

.progressbar li.completed:before {
    border-color: #28a745;
    background-color: #28a745;
    color: white;
    box-shadow: 0 2px 10px rgba(40, 167, 69, 0.3);
}

.progressbar li.completed:after {
    background-color: #28a745;
}

/* Étape actuelle */
.progressbar li.active {
    color: #6f42c1;
}

.progressbar li.active:before {
    border-color: #6f42c1;
    background-color: white;
    color: #6f42c1;
}

.progressbar li.active.current:before {
    background-color: #6f42c1;
    color: white;
    box-shadow: 0 2px 10px rgba(111, 66, 193, 0.3);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(111, 66, 193, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(111, 66, 193, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(111, 66, 193, 0);
    }
}

.progressbar li.active:after {
    background-color: #6f42c1;
}

/* Étapes en attente */
.progressbar li.pending {
    color: #6c757d;
}

.progressbar li.pending:before {
    border-color: #6c757d;
    background-color: white;
    color: #6c757d;
}

/* Badges d'état des étapes */
.stage-badge {
    display: inline-block;
    margin-left: 5px;
    font-size: 12px;
}

.stage-badge.completed {
    color: #28a745;
}

.stage-badge.current {
    color: #6f42c1;
}

.stage-badge.pending {
    color: #6c757d;
}

/* Responsive design */
@media (max-width: 992px) {
    .progressbar li {
        width: 50%; /* Deux étapes par ligne sur tablette */
    }

    .progressbar li:nth-child(2n+1):after {
        content: none;
    }
}

@media (max-width: 768px) {
    .progressbar li {
        width: 100%; /* Une étape par ligne sur mobile */
        text-align: left;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }

    .progressbar li:before {
        margin: 0 15px 0 0;
        display: inline-block;
        width: 30px;
        height: 30px;
        line-height: 28px;
    }

    .progressbar li:after {
        display: none;
    }
}
