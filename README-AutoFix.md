# AutoFix Pro - Système de Gestion d'Atelier Automobile

<p align="center">
  <img src="public/assets/images/logo.png" width="200" alt="AutoFix Pro Logo">
</p>

<p align="center">
  <img src="https://img.shields.io/badge/Laravel-12.x-red.svg" alt="Laravel Version">
  <img src="https://img.shields.io/badge/PHP-8.2+-blue.svg" alt="PHP Version">
  <img src="https://img.shields.io/badge/License-MIT-green.svg" alt="License">
</p>

## À propos d'AutoFix Pro

AutoFix Pro est un système complet de gestion d'atelier automobile développé avec Laravel 12. Il offre une solution moderne et intuitive pour gérer tous les aspects d'un atelier de réparation automobile, de la prise de rendez-vous à la livraison du véhicule.

## Fonctionnalités Principales

### 🚗 Gestion des Véhicules et Clients
- Base de données complète des clients (particuliers et professionnels)
- Gestion des véhicules avec historique des interventions
- Système de recherche avancé

### 📅 Calendrier et Rendez-vous
- Calendrier interactif avec vue mensuelle, hebdomadaire et quotidienne
- Gestion des créneaux disponibles
- Notifications automatiques
- Système de priorités

### 🔧 Atelier et Workflow
- Suivi des étapes de réparation en temps réel
- Gestion des photos avant/après intervention
- Évaluation des dommages et devis
- Gestion des pièces détachées
- Contrôle qualité

### 📊 Tableaux de Bord et KPI
- Statistiques en temps réel
- KPI annuels et mensuels
- Performance par technicien
- Analyses financières

### 👥 Gestion des Utilisateurs
- Système de rôles (Admin, Technicien)
- Profils personnalisables
- Préférences de notifications
- Historique d'activité

## Technologies Utilisées

- **Backend**: Laravel 12, PHP 8.2+
- **Frontend**: Blade Templates, Bootstrap 5, jQuery
- **Base de données**: MySQL/SQLite
- **Graphiques**: Chart.js, ApexCharts
- **Calendrier**: FullCalendar
- **Styles**: CSS3, SCSS, Tailwind CSS

## Installation

### Prérequis
- PHP 8.2 ou supérieur
- Composer
- Node.js et NPM
- MySQL ou SQLite

### Étapes d'installation

1. **Cloner le projet**
```bash
git clone https://github.com/votre-repo/autofix-pro.git
cd autofix-pro
```

2. **Installer les dépendances PHP**
```bash
composer install
```

3. **Installer les dépendances JavaScript**
```bash
npm install
```

4. **Configuration de l'environnement**
```bash
cp .env.example .env
php artisan key:generate
```

5. **Configuration de la base de données**
Modifier le fichier `.env` avec vos paramètres de base de données :
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=autofix_pro
DB_USERNAME=root
DB_PASSWORD=
```

6. **Exécuter les migrations et seeders**
```bash
php artisan migrate --seed
```

7. **Créer le lien symbolique pour le stockage**
```bash
php artisan storage:link
```

8. **Compiler les assets**
```bash
npm run build
```

9. **Démarrer le serveur de développement**
```bash
php artisan serve
```

## Comptes par défaut

Après l'installation, vous pouvez vous connecter avec :

**Administrateur**
- Email: `<EMAIL>`
- Mot de passe: `admin123`

**Technicien**
- Email: `<EMAIL>`
- Mot de passe: `tech123`

## Structure du Projet

```
autofix-pro/
├── app/
│   ├── Http/Controllers/     # Contrôleurs
│   ├── Models/              # Modèles Eloquent
│   └── ...
├── database/
│   ├── migrations/          # Migrations de base de données
│   └── seeders/            # Données de test
├── resources/
│   ├── views/              # Templates Blade
│   ├── css/                # Styles CSS
│   └── js/                 # Scripts JavaScript
├── public/
│   └── assets/             # Assets publics
└── routes/
    └── web.php             # Routes web
```

## Fonctionnalités Détaillées

### Workflow d'Atelier
1. **Demande à traiter** - Nouvelle demande de RDV
2. **Photos avant** - Documentation de l'état initial
3. **Évaluation** - Diagnostic et devis
4. **Commande pièces** - Gestion des approvisionnements
5. **Réparation** - Intervention technique
6. **Contrôle qualité** - Vérification des travaux
7. **Nettoyage** - Préparation à la livraison
8. **Photos après** - Documentation finale
9. **Facturation** - Établissement de la facture
10. **Livraison** - Remise du véhicule

### Système de Notifications
- Notifications email et SMS
- Rappels automatiques
- Alertes de priorité
- Notifications de changement d'étape

### Rapports et Exports
- Export PDF des KPI
- Export des données clients (RGPD)
- Rapports de performance
- Historiques détaillés

## Personnalisation

### Thèmes
Le système supporte plusieurs thèmes :
- Thème clair
- Thème sombre
- Thème automatique (selon les préférences système)

### Configuration
Modifiez les paramètres dans :
- `config/app.php` - Configuration générale
- `config/autofix.php` - Configuration spécifique (à créer)

## Améliorations Apportées

### ✅ Corrections et Améliorations Réalisées

1. **Modèles de Données Complets**
   - ✅ Modèle User mis à jour avec les champs appropriés
   - ✅ Nouveau modèle Client avec relations
   - ✅ Nouveau modèle Car (Véhicule)
   - ✅ Modèle Rdv amélioré avec toutes les relations
   - ✅ Nouveau modèle RdvStage pour le suivi des étapes
   - ✅ Modèle StagePhoto mis à jour
   - ✅ Nouveau modèle DamageAssessment
   - ✅ Nouveau modèle RequiredPart
   - ✅ Modèle Stage amélioré

2. **Contrôleurs Professionnels**
   - ✅ AtelierController amélioré
   - ✅ Nouveau ClientController complet
   - ✅ Nouveau CalendrierController avec FullCalendar
   - ✅ Nouveau KpiController pour les statistiques
   - ✅ Nouveau ProfileController pour la gestion des profils
   - ✅ LoginController amélioré

3. **Vues Modernes et Responsives**
   - ✅ Layout principal amélioré
   - ✅ Page d'accueil avec statistiques en temps réel
   - ✅ Interface atelier complète avec gestion des étapes
   - ✅ Page clients avec recherche et filtres
   - ✅ Calendrier interactif avec FullCalendar
   - ✅ Tableau de bord KPI avec graphiques
   - ✅ Page profil utilisateur complète
   - ✅ Page de connexion améliorée

4. **Base de Données Structurée**
   - ✅ Migrations complètes pour toutes les tables
   - ✅ Relations entre les tables bien définies
   - ✅ Index pour optimiser les performances
   - ✅ Seeders avec données de test

5. **Système de Routes Organisé**
   - ✅ Routes groupées par fonctionnalité
   - ✅ Middleware d'authentification
   - ✅ Noms de routes cohérents
   - ✅ API routes pour AJAX

6. **Design et UX**
   - ✅ CSS personnalisé moderne
   - ✅ Animations et transitions fluides
   - ✅ Design responsive
   - ✅ Thèmes clair/sombre
   - ✅ Icônes Font Awesome
   - ✅ Composants Bootstrap personnalisés

7. **Fonctionnalités Avancées**
   - ✅ Système de notifications
   - ✅ Gestion des photos
   - ✅ Export de données
   - ✅ Recherche et filtres
   - ✅ Statistiques en temps réel
   - ✅ Workflow d'atelier complet

## Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/AmazingFeature`)
3. Commit vos changements (`git commit -m 'Add some AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

## Sécurité

Si vous découvrez une vulnérabilité de sécurité, veuillez envoyer un email à [<EMAIL>](mailto:<EMAIL>).

## Licence

Ce projet est sous licence MIT. Voir le fichier [LICENSE](LICENSE) pour plus de détails.

## Support

- Documentation: [Wiki du projet](https://github.com/votre-repo/autofix-pro/wiki)
- Issues: [GitHub Issues](https://github.com/votre-repo/autofix-pro/issues)
- Email: [<EMAIL>](mailto:<EMAIL>)

## Roadmap

- [ ] Application mobile
- [ ] API REST complète
- [ ] Intégration avec systèmes de paiement
- [ ] Module de facturation avancé
- [ ] Système de réservation en ligne
- [ ] Intégration avec fournisseurs de pièces

---

Développé avec ❤️ par l'équipe AutoFix Pro
