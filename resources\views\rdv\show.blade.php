@extends('layout')

@section('content')
<div class="pcoded-main-container">
    <div class="pcoded-content">
        <!-- [ breadcrumb ] start -->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10">Détails du Rendez-vous #{{ $rdv->id }}</h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}"><i class="feather icon-home"></i></a></li>
                            <li class="breadcrumb-item"><a href="{{ route('calendrier.index') }}">Calendrier</a></li>
                            <li class="breadcrumb-item"><a href="#!">Détails RDV</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!-- [ breadcrumb ] end -->

        <!-- [ Main Content ] start -->
        <div class="row">
            <!-- [ Informations Générales ] start -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle mr-2"></i>Informations du Rendez-vous</h5>
                        <div class="card-header-right">
                            <a href="{{ route('rdv.edit', $rdv) }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-edit mr-1"></i>Modifier
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Date RDV:</strong></td>
                                        <td>{{ $rdv->jour_rdv->format('d/m/Y') }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Heure:</strong></td>
                                        <td>{{ $rdv->heure_rdv->format('H:i') }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Type:</strong></td>
                                        <td>{{ $rdv->type_rdv }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Statut:</strong></td>
                                        <td>
                                            @php
                                                $statusColors = [
                                                    'en_attente' => 'warning',
                                                    'confirme' => 'info',
                                                    'en_cours' => 'primary',
                                                    'termine' => 'success',
                                                    'annule' => 'danger'
                                                ];
                                                $color = $statusColors[$rdv->statut] ?? 'secondary';
                                            @endphp
                                            <span class="badge badge-{{ $color }}">{{ ucfirst(str_replace('_', ' ', $rdv->statut)) }}</span>
                                        </td>
                                    </tr>
                                    @if($rdv->priority)
                                        <tr>
                                            <td><strong>Priorité:</strong></td>
                                            <td>
                                                @php
                                                    $priorityColors = [1 => 'danger', 2 => 'warning', 3 => 'info', 4 => 'secondary', 5 => 'light'];
                                                    $priorityLabels = [1 => 'Très Haute', 2 => 'Haute', 3 => 'Normale', 4 => 'Basse', 5 => 'Très Basse'];
                                                @endphp
                                                <span class="badge badge-{{ $priorityColors[$rdv->priority] ?? 'secondary' }}">
                                                    {{ $priorityLabels[$rdv->priority] ?? 'Normale' }}
                                                </span>
                                            </td>
                                        </tr>
                                    @endif
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    @if($rdv->cost_estimate)
                                        <tr>
                                            <td><strong>Devis:</strong></td>
                                            <td>{{ number_format($rdv->cost_estimate, 2) }} €</td>
                                        </tr>
                                    @endif
                                    @if($rdv->final_cost)
                                        <tr>
                                            <td><strong>Coût Final:</strong></td>
                                            <td>{{ number_format($rdv->final_cost, 2) }} €</td>
                                        </tr>
                                    @endif
                                    @if($rdv->estimated_duration)
                                        <tr>
                                            <td><strong>Durée Estimée:</strong></td>
                                            <td>{{ $rdv->estimated_duration }} heures</td>
                                        </tr>
                                    @endif
                                    @if($rdv->actual_duration)
                                        <tr>
                                            <td><strong>Durée Réelle:</strong></td>
                                            <td>{{ $rdv->actual_duration }} heures</td>
                                        </tr>
                                    @endif
                                    @if($rdv->assignedTo)
                                        <tr>
                                            <td><strong>Assigné à:</strong></td>
                                            <td>{{ $rdv->assignedTo->prenom }} {{ $rdv->assignedTo->nom }}</td>
                                        </tr>
                                    @endif
                                </table>
                            </div>
                        </div>

                        @if($rdv->description)
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6>Description:</h6>
                                    <p class="text-muted">{{ $rdv->description }}</p>
                                </div>
                            </div>
                        @endif

                        @if($rdv->notes)
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6>Notes:</h6>
                                    <p class="text-muted">{{ $rdv->notes }}</p>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- [ Étapes du RDV ] start -->
                @if($rdv->rdvStages->count() > 0)
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-tasks mr-2"></i>Historique des Étapes</h5>
                        </div>
                        <div class="card-body">
                            <div class="timeline">
                                @foreach($rdv->rdvStages->sortBy('created_at') as $stage)
                                    <div class="timeline-item">
                                        <div class="timeline-marker bg-{{ $stage->status === 'termine' ? 'success' : 'primary' }}">
                                            <i class="fas fa-{{ $stage->status === 'termine' ? 'check' : 'clock' }}"></i>
                                        </div>
                                        <div class="timeline-content">
                                            <h6>{{ $stage->stage->name ?? 'Étape inconnue' }}</h6>
                                            <p class="text-muted mb-1">
                                                Statut: <span class="badge badge-{{ $stage->status === 'termine' ? 'success' : 'primary' }}">
                                                    {{ ucfirst($stage->status) }}
                                                </span>
                                            </p>
                                            <small class="text-muted">
                                                {{ $stage->created_at->format('d/m/Y H:i') }}
                                                @if($stage->user)
                                                    par {{ $stage->user->prenom }} {{ $stage->user->nom }}
                                                @endif
                                            </small>
                                            @if($stage->notes)
                                                <p class="mt-2">{{ $stage->notes }}</p>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif
                <!-- [ Étapes du RDV ] end -->
            </div>

            <!-- [ Informations Client et Véhicule ] start -->
            <div class="col-md-4">
                <!-- [ Client ] start -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-user mr-2"></i>Client</h5>
                        @if($rdv->client)
                            <div class="card-header-right">
                                <a href="{{ route('clients.show', $rdv->client) }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye mr-1"></i>Voir
                                </a>
                            </div>
                        @endif
                    </div>
                    <div class="card-body">
                        @if($rdv->client)
                            <h6>{{ $rdv->client->prenom }} {{ $rdv->client->nom }}</h6>
                            @if($rdv->client->email)
                                <p class="mb-1"><i class="fas fa-envelope mr-1"></i>{{ $rdv->client->email }}</p>
                            @endif
                            @if($rdv->client->telephone)
                                <p class="mb-1"><i class="fas fa-phone mr-1"></i>{{ $rdv->client->telephone }}</p>
                            @endif
                            @if($rdv->client->adresse)
                                <p class="mb-0"><i class="fas fa-map-marker-alt mr-1"></i>{{ $rdv->client->adresse }}</p>
                            @endif
                        @else
                            <p class="text-muted">Client non défini</p>
                        @endif
                    </div>
                </div>
                <!-- [ Client ] end -->

                <!-- [ Véhicule ] start -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-car mr-2"></i>Véhicule</h5>
                    </div>
                    <div class="card-body">
                        @if($rdv->car)
                            <h6>{{ $rdv->car->marque }} {{ $rdv->car->modele }}</h6>
                            <p class="mb-1"><strong>Année:</strong> {{ $rdv->car->annee }}</p>
                            <p class="mb-1"><strong>Immatriculation:</strong> 
                                <span class="badge badge-secondary">{{ $rdv->car->immatriculation }}</span>
                            </p>
                            @if($rdv->car->couleur)
                                <p class="mb-1"><strong>Couleur:</strong> {{ $rdv->car->couleur }}</p>
                            @endif
                            @if($rdv->car->kilometrage)
                                <p class="mb-1"><strong>Kilométrage:</strong> {{ number_format($rdv->car->kilometrage) }} km</p>
                            @endif
                            @if($rdv->car->carburant)
                                <p class="mb-0"><strong>Carburant:</strong> {{ $rdv->car->carburant }}</p>
                            @endif
                        @else
                            <p class="text-muted">Véhicule non défini</p>
                        @endif
                    </div>
                </div>
                <!-- [ Véhicule ] end -->

                <!-- [ Actions ] start -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cogs mr-2"></i>Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{{ route('rdv.edit', $rdv) }}" class="btn btn-primary">
                                <i class="fas fa-edit mr-1"></i>Modifier RDV
                            </a>
                            @if($rdv->statut !== 'termine')
                                <a href="{{ route('workshop.details', $rdv) }}" class="btn btn-info">
                                    <i class="fas fa-tools mr-1"></i>Gérer dans l'Atelier
                                </a>
                            @endif
                            <button class="btn btn-warning" onclick="printRdv()">
                                <i class="fas fa-print mr-1"></i>Imprimer
                            </button>
                            @if($rdv->statut === 'en_attente')
                                <button class="btn btn-danger" onclick="cancelRdv()">
                                    <i class="fas fa-times mr-1"></i>Annuler
                                </button>
                            @endif
                        </div>
                    </div>
                </div>
                <!-- [ Actions ] end -->
            </div>
        </div>
        <!-- [ Main Content ] end -->
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}
</style>

<script>
function printRdv() {
    window.print();
}

function cancelRdv() {
    if (confirm('Êtes-vous sûr de vouloir annuler ce rendez-vous ?')) {
        // Logique d'annulation
        alert('Fonctionnalité d\'annulation à implémenter');
    }
}
</script>
@endsection
