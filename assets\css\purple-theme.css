/* CSS personnalisé pour le thème violet */

/* Définir la couleur violette pour les badges "En cours" */
.stage-badge.current {
    color: #6f42c1 !important;
}

/* Étape actuelle */
.progressbar li.active {
    color: #6f42c1 !important;
}

.progressbar li.active:before {
    border-color: #6f42c1 !important;
    background-color: white !important;
    color: #6f42c1 !important;
}

.progressbar li.active.current:before {
    background-color: #6f42c1 !important;
    color: white !important;
    box-shadow: 0 2px 10px rgba(111, 66, 193, 0.3) !important;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(111, 66, 193, 0.4) !important;
    }
    70% {
        box-shadow: 0 0 0 10px rgba(111, 66, 193, 0) !important;
    }
    100% {
        box-shadow: 0 0 0 0 rgba(111, 66, 193, 0) !important;
    }
}

.progressbar li.active:after {
    background-color: #6f42c1 !important;
}

/* Couleur pour les badges */
.bg-purple {
    background-color: #6f42c1 !important;
    color: white !important;
}

.text-purple {
    color: #6f42c1 !important;
}

/* Surcharger les variables CSS */
:root {
    --primary-dark: #6f42c1 !important;
    --primary-dark-rgb: 111, 66, 193 !important;
}

/* Couleur pour les événements en cours */
.en_cours {
    background-color: #6f42c1 !important;
}

.en_cours .badge-circle {
    color: #6f42c1 !important;
}

/* Badge de statut en cours */
.status-en_cours {
    background-color: #6f42c1 !important;
}

/* Couleurs pour les informations de véhicule selon le statut */
.vehicle-info-en_cours {
    border-left-color: #6f42c1 !important;
    color: #6f42c1 !important;
}

.appointment-card .vehicle-info-en_cours {
    background-color: rgba(111, 66, 193, 0.1) !important;
    color: #6f42c1 !important;
}
