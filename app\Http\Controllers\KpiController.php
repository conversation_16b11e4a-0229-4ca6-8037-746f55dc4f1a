<?php

namespace App\Http\Controllers;

use App\Models\Rdv;
use App\Models\Client;
use App\Models\Car;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class KpiController extends Controller
{
    /**
     * Afficher les KPI annuels
     */
    public function index(Request $request)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $year = $request->input('year', now()->year);
        $startDate = Carbon::createFromDate($year, 1, 1)->startOfYear();
        $endDate = Carbon::createFromDate($year, 12, 31)->endOfYear();

        // KPI généraux
        $generalKpis = $this->getGeneralKpis($startDate, $endDate);
        
        // KPI mensuels
        $monthlyKpis = $this->getMonthlyKpis($year);
        
        // KPI par technicien
        $technicianKpis = $this->getTechnicianKpis($startDate, $endDate);
        
        // KPI financiers
        $financialKpis = $this->getFinancialKpis($startDate, $endDate);
        
        // Tendances
        $trends = $this->getTrends($year);

        return view('kpi.index', compact(
            'generalKpis', 
            'monthlyKpis', 
            'technicianKpis', 
            'financialKpis', 
            'trends', 
            'year'
        ));
    }

    /**
     * KPI généraux
     */
    private function getGeneralKpis($startDate, $endDate)
    {
        $totalRdv = Rdv::whereBetween('jour_rdv', [$startDate, $endDate])->count();
        $rdvTermines = Rdv::whereBetween('jour_rdv', [$startDate, $endDate])
            ->where('statut', 'termine')->count();
        $rdvAnnules = Rdv::whereBetween('jour_rdv', [$startDate, $endDate])
            ->where('statut', 'annule')->count();
        
        $tauxReussite = $totalRdv > 0 ? round(($rdvTermines / $totalRdv) * 100, 2) : 0;
        $tauxAnnulation = $totalRdv > 0 ? round(($rdvAnnules / $totalRdv) * 100, 2) : 0;

        $nouveauxClients = Client::whereBetween('created_at', [$startDate, $endDate])->count();
        $nouveauxVehicules = Car::whereBetween('created_at', [$startDate, $endDate])->count();

        // Temps moyen de réparation
        $tempsReparation = DB::table('rdv_stages')
            ->join('rdv', 'rdv_stages.rdv_id', '=', 'rdv.id')
            ->whereBetween('rdv.jour_rdv', [$startDate, $endDate])
            ->whereNotNull('rdv_stages.actual_duration')
            ->avg('rdv_stages.actual_duration');

        return [
            'total_rdv' => $totalRdv,
            'rdv_termines' => $rdvTermines,
            'rdv_annules' => $rdvAnnules,
            'taux_reussite' => $tauxReussite,
            'taux_annulation' => $tauxAnnulation,
            'nouveaux_clients' => $nouveauxClients,
            'nouveaux_vehicules' => $nouveauxVehicules,
            'temps_moyen_reparation' => round($tempsReparation ?? 0, 0)
        ];
    }

    /**
     * KPI mensuels
     */
    private function getMonthlyKpis($year)
    {
        $monthlyData = [];
        
        for ($month = 1; $month <= 12; $month++) {
            $startDate = Carbon::createFromDate($year, $month, 1)->startOfMonth();
            $endDate = Carbon::createFromDate($year, $month, 1)->endOfMonth();
            
            $rdvCount = Rdv::whereBetween('jour_rdv', [$startDate, $endDate])->count();
            $rdvTermines = Rdv::whereBetween('jour_rdv', [$startDate, $endDate])
                ->where('statut', 'termine')->count();
            $chiffreAffaires = Rdv::whereBetween('jour_rdv', [$startDate, $endDate])
                ->where('statut', 'termine')
                ->sum('final_cost');
            
            $monthlyData[] = [
                'month' => $month,
                'month_name' => Carbon::createFromDate($year, $month, 1)->format('F'),
                'rdv_count' => $rdvCount,
                'rdv_termines' => $rdvTermines,
                'chiffre_affaires' => $chiffreAffaires ?? 0
            ];
        }
        
        return $monthlyData;
    }

    /**
     * KPI par technicien
     */
    private function getTechnicianKpis($startDate, $endDate)
    {
        $techniciens = User::where('role', 'technicien')->where('active', true)->get();
        $technicianData = [];
        
        foreach ($techniciens as $technicien) {
            $rdvAssignes = Rdv::whereBetween('jour_rdv', [$startDate, $endDate])
                ->where('assigned_to', $technicien->id)->count();
            
            $rdvTermines = Rdv::whereBetween('jour_rdv', [$startDate, $endDate])
                ->where('assigned_to', $technicien->id)
                ->where('statut', 'termine')->count();
            
            $tempsTotal = DB::table('rdv_stages')
                ->join('rdv', 'rdv_stages.rdv_id', '=', 'rdv.id')
                ->whereBetween('rdv.jour_rdv', [$startDate, $endDate])
                ->where('rdv_stages.user_id', $technicien->id)
                ->whereNotNull('rdv_stages.actual_duration')
                ->sum('rdv_stages.actual_duration');
            
            $tauxReussite = $rdvAssignes > 0 ? round(($rdvTermines / $rdvAssignes) * 100, 2) : 0;
            
            $technicianData[] = [
                'technicien' => $technicien,
                'rdv_assignes' => $rdvAssignes,
                'rdv_termines' => $rdvTermines,
                'taux_reussite' => $tauxReussite,
                'temps_total' => round($tempsTotal ?? 0, 0)
            ];
        }
        
        return $technicianData;
    }

    /**
     * KPI financiers
     */
    private function getFinancialKpis($startDate, $endDate)
    {
        $chiffreAffaires = Rdv::whereBetween('jour_rdv', [$startDate, $endDate])
            ->where('statut', 'termine')
            ->sum('final_cost');
        
        $chiffreAffairesEstime = Rdv::whereBetween('jour_rdv', [$startDate, $endDate])
            ->whereIn('statut', ['confirme', 'en_cours'])
            ->sum('cost_estimate');
        
        $coutPieces = DB::table('required_parts')
            ->join('rdv', 'required_parts.rdv_id', '=', 'rdv.id')
            ->whereBetween('rdv.jour_rdv', [$startDate, $endDate])
            ->where('required_parts.status', 'installed')
            ->sum('required_parts.total_price');
        
        $panierMoyen = $chiffreAffaires > 0 ? 
            round($chiffreAffaires / Rdv::whereBetween('jour_rdv', [$startDate, $endDate])
                ->where('statut', 'termine')->count(), 2) : 0;
        
        return [
            'chiffre_affaires' => $chiffreAffaires ?? 0,
            'chiffre_affaires_estime' => $chiffreAffairesEstime ?? 0,
            'cout_pieces' => $coutPieces ?? 0,
            'panier_moyen' => $panierMoyen,
            'marge_pieces' => ($chiffreAffaires ?? 0) - ($coutPieces ?? 0)
        ];
    }

    /**
     * Tendances
     */
    private function getTrends($year)
    {
        $currentYear = $year;
        $previousYear = $year - 1;
        
        $currentYearData = $this->getGeneralKpis(
            Carbon::createFromDate($currentYear, 1, 1)->startOfYear(),
            Carbon::createFromDate($currentYear, 12, 31)->endOfYear()
        );
        
        $previousYearData = $this->getGeneralKpis(
            Carbon::createFromDate($previousYear, 1, 1)->startOfYear(),
            Carbon::createFromDate($previousYear, 12, 31)->endOfYear()
        );
        
        $trends = [];
        
        foreach ($currentYearData as $key => $value) {
            $previousValue = $previousYearData[$key] ?? 0;
            $trend = 0;
            
            if ($previousValue > 0) {
                $trend = round((($value - $previousValue) / $previousValue) * 100, 2);
            } elseif ($value > 0) {
                $trend = 100;
            }
            
            $trends[$key] = [
                'current' => $value,
                'previous' => $previousValue,
                'trend' => $trend,
                'direction' => $trend > 0 ? 'up' : ($trend < 0 ? 'down' : 'stable')
            ];
        }
        
        return $trends;
    }

    /**
     * Exporter les KPI en PDF
     */
    public function exportPdf(Request $request)
    {
        $year = $request->input('year', now()->year);
        
        // Récupérer toutes les données KPI
        $startDate = Carbon::createFromDate($year, 1, 1)->startOfYear();
        $endDate = Carbon::createFromDate($year, 12, 31)->endOfYear();
        
        $data = [
            'year' => $year,
            'generalKpis' => $this->getGeneralKpis($startDate, $endDate),
            'monthlyKpis' => $this->getMonthlyKpis($year),
            'technicianKpis' => $this->getTechnicianKpis($startDate, $endDate),
            'financialKpis' => $this->getFinancialKpis($startDate, $endDate),
            'generated_at' => now()->format('d/m/Y H:i')
        ];
        
        // Ici vous pouvez utiliser une librairie comme DomPDF
        // Pour l'instant, on retourne une vue
        return view('kpi.pdf', $data);
    }

    /**
     * API pour les graphiques
     */
    public function getChartData(Request $request)
    {
        $year = $request->input('year', now()->year);
        $type = $request->input('type', 'monthly');
        
        switch ($type) {
            case 'monthly':
                return response()->json($this->getMonthlyKpis($year));
            
            case 'technician':
                $startDate = Carbon::createFromDate($year, 1, 1)->startOfYear();
                $endDate = Carbon::createFromDate($year, 12, 31)->endOfYear();
                return response()->json($this->getTechnicianKpis($startDate, $endDate));
            
            default:
                return response()->json(['error' => 'Type non supporté'], 400);
        }
    }
}
