@php

// Récupérer le logo depuis les paramètres
$logoPath = function_exists('getSetting') ? getSetting('company_logo', 'assets/images/logo.png') : 'assets/images/logo.png';
$companyName = function_exists('getSetting') ? getSetting('company_name', 'AUTOFIX') : 'AUTOFIX';
@endphp
<header class="navbar pcoded-header navbar-expand-lg navbar-light header-blue">


				<div class="m-header">
					<a class="mobile-menu" id="mobile-collapse" href="#!"><span></span></a>
					<a href="{{url('/')}}" class="b-brand">
						<!-- ========   Logo de l'entreprise   ============ -->
						<img src="<?= htmlspecialchars($logoPath) ?>" alt="<?= htmlspecialchars($companyName) ?>" class="logo" width="150" height="50">
						<img src="assets/images/logo-icon.png" alt="" class="logo-thumb">
					</a>
				</div>
</header>