@extends('layout')

@section('content')
<div class="pcoded-main-container">
    <div class="pcoded-content">
        <!-- [ breadcrumb ] start -->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10">Calendrier des Rendez-vous</h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}"><i class="feather icon-home"></i></a></li>
                            <li class="breadcrumb-item"><a href="#!">Calendrier</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!-- [ breadcrumb ] end -->

        <!-- [ Main Content ] start -->
        <div class="row">
            <!-- [ Statistiques de la période ] start -->
            <div class="col-md-6 col-xl-2">
                <div class="card bg-c-blue order-card">
                    <div class="card-body">
                        <h6 class="text-white">Total</h6>
                        <h2 class="text-white">{{ $stats['total'] }}</h2>
                        <p class="m-b-0">RDV</p>
                        <i class="fas fa-calendar f-right f-26 text-c-blue"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-xl-2">
                <div class="card bg-c-yellow order-card">
                    <div class="card-body">
                        <h6 class="text-white">En Attente</h6>
                        <h2 class="text-white">{{ $stats['en_attente'] }}</h2>
                        <p class="m-b-0">RDV</p>
                        <i class="fas fa-clock f-right f-26 text-c-yellow"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-xl-2">
                <div class="card bg-c-green order-card">
                    <div class="card-body">
                        <h6 class="text-white">Confirmés</h6>
                        <h2 class="text-white">{{ $stats['confirme'] }}</h2>
                        <p class="m-b-0">RDV</p>
                        <i class="fas fa-check f-right f-26 text-c-green"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-xl-2">
                <div class="card bg-c-blue order-card">
                    <div class="card-body">
                        <h6 class="text-white">En Cours</h6>
                        <h2 class="text-white">{{ $stats['en_cours'] }}</h2>
                        <p class="m-b-0">RDV</p>
                        <i class="fas fa-cog f-right f-26 text-c-blue"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-xl-2">
                <div class="card bg-c-green order-card">
                    <div class="card-body">
                        <h6 class="text-white">Terminés</h6>
                        <h2 class="text-white">{{ $stats['termine'] }}</h2>
                        <p class="m-b-0">RDV</p>
                        <i class="fas fa-check-circle f-right f-26 text-c-green"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-xl-2">
                <div class="card bg-c-red order-card">
                    <div class="card-body">
                        <h6 class="text-white">Annulés</h6>
                        <h2 class="text-white">{{ $stats['annule'] }}</h2>
                        <p class="m-b-0">RDV</p>
                        <i class="fas fa-times f-right f-26 text-c-red"></i>
                    </div>
                </div>
            </div>
            <!-- [ Statistiques de la période ] end -->

            <!-- [ Contrôles du calendrier ] start -->
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-calendar-alt mr-2"></i>Calendrier</h5>
                        <div class="card-header-right">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="changeView('month')">
                                    Mois
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="changeView('week')">
                                    Semaine
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="changeView('day')">
                                    Jour
                                </button>
                            </div>
                            <button type="button" class="btn btn-sm btn-primary ml-2" data-toggle="modal" data-target="#newRdvModal">
                                <i class="fas fa-plus mr-1"></i>Nouveau RDV
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Navigation de date -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-secondary" onclick="navigateDate('prev')">
                                        <i class="fas fa-chevron-left"></i> Précédent
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="navigateDate('today')">
                                        Aujourd'hui
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="navigateDate('next')">
                                        Suivant <i class="fas fa-chevron-right"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6 text-right">
                                <h5 id="currentPeriod">
                                    @if($view === 'month')
                                        {{ $currentDate->format('F Y') }}
                                    @elseif($view === 'week')
                                        Semaine du {{ $currentDate->startOfWeek()->format('d/m/Y') }}
                                    @else
                                        {{ $currentDate->format('d/m/Y') }}
                                    @endif
                                </h5>
                            </div>
                        </div>

                        <!-- Calendrier -->
                        <div id="calendar"></div>
                    </div>
                </div>
            </div>
            <!-- [ Contrôles du calendrier ] end -->

            <!-- [ Liste des RDV ] start -->
            @if($rdvs->count() > 0)
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list mr-2"></i>Rendez-vous de la période</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Date/Heure</th>
                                        <th>Client</th>
                                        <th>Véhicule</th>
                                        <th>Type</th>
                                        <th>Statut</th>
                                        <th>Technicien</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($rdvs as $rdv)
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong>{{ $rdv->jour_rdv->format('d/m/Y') }}</strong>
                                                    <br><small class="text-muted">{{ $rdv->heure_rdv->format('H:i') }}</small>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong>{{ $rdv->client->full_name }}</strong>
                                                    @if($rdv->client->telephone)
                                                        <br><small class="text-muted">{{ $rdv->client->telephone }}</small>
                                                    @endif
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong>{{ $rdv->car->marque }} {{ $rdv->car->modele }}</strong>
                                                    <br><small class="text-muted">{{ $rdv->car->immatriculation }}</small>
                                                </div>
                                            </td>
                                            <td>{{ $rdv->type_rdv }}</td>
                                            <td>
                                                @php
                                                    $statusColors = [
                                                        'en_attente' => 'warning',
                                                        'confirme' => 'info',
                                                        'en_cours' => 'primary',
                                                        'termine' => 'success',
                                                        'annule' => 'danger'
                                                    ];
                                                    $statusColor = $statusColors[$rdv->statut] ?? 'secondary';
                                                @endphp
                                                <span class="badge badge-{{ $statusColor }}">
                                                    {{ $rdv->formatted_status }}
                                                </span>
                                            </td>
                                            <td>
                                                @if($rdv->assignedTo)
                                                    {{ $rdv->assignedTo->prenom }} {{ $rdv->assignedTo->nom }}
                                                @else
                                                    <span class="text-muted">Non assigné</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-primary"
                                                            onclick="viewRdv({{ $rdv->id }})">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-warning"
                                                            onclick="editRdv({{ $rdv->id }})">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    @if($rdv->statut !== 'en_cours')
                                                        <button type="button" class="btn btn-sm btn-danger"
                                                                onclick="deleteRdv({{ $rdv->id }})">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            @endif
            <!-- [ Liste des RDV ] end -->
        </div>
        <!-- [ Main Content ] end -->
    </div>
</div>

<!-- [ Modal Nouveau RDV ] start -->
<div class="modal fade" id="newRdvModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Nouveau Rendez-vous</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="newRdvForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="client_id">Client *</label>
                                <select class="form-control" id="client_id" name="client_id" required>
                                    <option value="">Sélectionner un client</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="car_id">Véhicule *</label>
                                <select class="form-control" id="car_id" name="car_id" required>
                                    <option value="">Sélectionner un véhicule</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="jour_rdv">Date *</label>
                                <input type="date" class="form-control" id="jour_rdv" name="jour_rdv" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="heure_rdv">Heure *</label>
                                <select class="form-control" id="heure_rdv" name="heure_rdv" required>
                                    <option value="">Sélectionner une heure</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="type_rdv">Type de RDV *</label>
                                <select class="form-control" id="type_rdv" name="type_rdv" required>
                                    <option value="">Sélectionner un type</option>
                                    <option value="Réparation">Réparation</option>
                                    <option value="Entretien">Entretien</option>
                                    <option value="Diagnostic">Diagnostic</option>
                                    <option value="Contrôle technique">Contrôle technique</option>
                                    <option value="Autre">Autre</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="priority">Priorité</label>
                                <select class="form-control" id="priority" name="priority">
                                    <option value="1">1 - Très faible</option>
                                    <option value="2">2 - Faible</option>
                                    <option value="3" selected>3 - Normale</option>
                                    <option value="4">4 - Élevée</option>
                                    <option value="5">5 - Urgente</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"
                                  placeholder="Décrivez le problème ou le travail à effectuer..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Créer le RDV</button>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- [ Modal Nouveau RDV ] end -->

@endsection

@push('styles')
<link href="https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.css" rel="stylesheet">
<style>
.fc-event {
    cursor: pointer;
}
.fc-event:hover {
    opacity: 0.8;
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/locales/fr.js"></script>
<script>
let calendar;
let currentView = '{{ $view }}';
let currentDate = '{{ $currentDate->format('Y-m-d') }}';

document.addEventListener('DOMContentLoaded', function() {
    initializeCalendar();
    loadClients();
});

function initializeCalendar() {
    const calendarEl = document.getElementById('calendar');

    calendar = new FullCalendar.Calendar(calendarEl, {
        locale: 'fr',
        initialView: currentView === 'month' ? 'dayGridMonth' : (currentView === 'week' ? 'timeGridWeek' : 'timeGridDay'),
        initialDate: currentDate,
        headerToolbar: false,
        height: 'auto',
        events: {
            url: '{{ route("calendrier.events") }}',
            failure: function() {
                alert('Erreur lors du chargement des événements');
            }
        },
        eventClick: function(info) {
            viewRdv(info.event.id);
        },
        dateClick: function(info) {
            $('#jour_rdv').val(info.dateStr);
            loadAvailableSlots(info.dateStr);
            $('#newRdvModal').modal('show');
        }
    });

    calendar.render();
}

function changeView(view) {
    currentView = view;
    const calendarView = view === 'month' ? 'dayGridMonth' : (view === 'week' ? 'timeGridWeek' : 'timeGridDay');
    calendar.changeView(calendarView);
    updateURL();
}

function navigateDate(direction) {
    if (direction === 'prev') {
        calendar.prev();
    } else if (direction === 'next') {
        calendar.next();
    } else if (direction === 'today') {
        calendar.today();
    }

    currentDate = calendar.getDate().toISOString().split('T')[0];
    updateURL();
}

function updateURL() {
    const url = new URL(window.location);
    url.searchParams.set('view', currentView);
    url.searchParams.set('date', currentDate);
    window.history.pushState({}, '', url);
}

function loadClients() {
    $.get('{{ route("clients.search") }}', function(clients) {
        const clientSelect = $('#client_id');
        clientSelect.empty().append('<option value="">Sélectionner un client</option>');

        clients.forEach(function(client) {
            clientSelect.append(`<option value="${client.id}">${client.prenom} ${client.nom}</option>`);
        });
    });
}

$('#client_id').change(function() {
    const clientId = $(this).val();
    const carSelect = $('#car_id');

    carSelect.empty().append('<option value="">Sélectionner un véhicule</option>');

    if (clientId) {
        $.get(`/clients/${clientId}/cars`, function(cars) {
            cars.forEach(function(car) {
                carSelect.append(`<option value="${car.id}">${car.marque} ${car.modele} (${car.immatriculation})</option>`);
            });
        }).fail(function(xhr) {
            console.error('Erreur lors du chargement des véhicules:', xhr.responseText);
            carSelect.append('<option value="">Erreur lors du chargement</option>');
        });
    }
});

$('#jour_rdv').change(function() {
    const date = $(this).val();
    if (date) {
        loadAvailableSlots(date);
    }
});

function loadAvailableSlots(date) {
    $.get('{{ route("calendrier.slots") }}', { date: date }, function(slots) {
        const heureSelect = $('#heure_rdv');
        heureSelect.empty().append('<option value="">Sélectionner une heure</option>');

        slots.forEach(function(slot) {
            heureSelect.append(`<option value="${slot.time}">${slot.label}</option>`);
        });
    });
}

$('#newRdvForm').submit(function(e) {
    e.preventDefault();

    $.ajax({
        url: '{{ route("calendrier.store") }}',
        method: 'POST',
        data: $(this).serialize() + '&_token={{ csrf_token() }}',
        success: function(response) {
            $('#newRdvModal').modal('hide');
            calendar.refetchEvents();
            alert('Rendez-vous créé avec succès');
        },
        error: function(xhr) {
            alert('Erreur: ' + xhr.responseJSON.message);
        }
    });
});

function viewRdv(rdvId) {
    // Ouvrir les détails du RDV
    window.open(`{{ route("rdv.show", "") }}/${rdvId}`, '_blank');
}

function editRdv(rdvId) {
    // Ouvrir l'édition du RDV
    window.location.href = `{{ route("rdv.edit", "") }}/${rdvId}`;
}

function deleteRdv(rdvId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce rendez-vous ?')) {
        $.ajax({
            url: `{{ route("calendrier.destroy", "") }}/${rdvId}`,
            method: 'DELETE',
            data: { _token: '{{ csrf_token() }}' },
            success: function(response) {
                calendar.refetchEvents();
                location.reload();
            },
            error: function(xhr) {
                alert('Erreur: ' + xhr.responseJSON.message);
            }
        });
    }
}
</script>
@endpush
