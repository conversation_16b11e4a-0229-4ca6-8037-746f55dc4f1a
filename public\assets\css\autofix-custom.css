/* AutoFix Pro - Custom Styles */

/* ===== Variables CSS ===== */
:root {
    --primary-color: #5A9BD4;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

/* ===== Améliorations générales ===== */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.btn {
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
}

.btn:hover {
    transform: translateY(-1px);
}

/* ===== Avatar Circle ===== */
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
    background: linear-gradient(135deg, var(--primary-color), #4a90e2);
}

.avatar-circle.large {
    width: 80px;
    height: 80px;
    font-size: 24px;
}

.avatar-circle.xl {
    width: 120px;
    height: 120px;
    font-size: 36px;
}

/* ===== Cards d'ordre ===== */
.order-card {
    background: linear-gradient(135deg, var(--primary-color), #4a90e2);
    border: none;
    border-radius: var(--border-radius);
    overflow: hidden;
    position: relative;
}

.order-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.bg-c-blue { background: linear-gradient(135deg, #667eea, #764ba2) !important; }
.bg-c-green { background: linear-gradient(135deg, #56ab2f, #a8e6cf) !important; }
.bg-c-yellow { background: linear-gradient(135deg, #f093fb, #f5576c) !important; }
.bg-c-red { background: linear-gradient(135deg, #ff6b6b, #ee5a24) !important; }

/* ===== Badges améliorés ===== */
.badge {
    border-radius: 20px;
    padding: 0.5em 0.8em;
    font-weight: 500;
    font-size: 0.75rem;
}

.badge-primary { background: linear-gradient(135deg, var(--primary-color), #4a90e2); }
.badge-success { background: linear-gradient(135deg, var(--success-color), #20c997); }
.badge-danger { background: linear-gradient(135deg, var(--danger-color), #e74c3c); }
.badge-warning { background: linear-gradient(135deg, var(--warning-color), #f39c12); }
.badge-info { background: linear-gradient(135deg, var(--info-color), #3498db); }

/* ===== Tables améliorées ===== */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table thead th {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(90, 155, 212, 0.05);
    transform: scale(1.01);
}

/* ===== Timeline ===== */
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, var(--primary-color), var(--info-color));
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 3px solid #fff;
    box-shadow: 0 0 0 3px var(--primary-color);
    background: var(--primary-color);
}

.timeline-content {
    background: #fff;
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border-left: 4px solid var(--primary-color);
    transition: var(--transition);
}

.timeline-content:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

/* ===== Progress bars ===== */
.progress {
    height: 8px;
    border-radius: 10px;
    background: #e9ecef;
    overflow: hidden;
}

.progress-bar {
    border-radius: 10px;
    transition: width 0.6s ease;
}

/* ===== Formulaires ===== */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(90, 155, 212, 0.25);
}

/* ===== Modals ===== */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 40px rgba(0,0,0,0.2);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color), #4a90e2);
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-header .close {
    color: white;
    opacity: 0.8;
}

.modal-header .close:hover {
    opacity: 1;
}

/* ===== Navigation ===== */
.pcoded-navbar .nav-link {
    transition: var(--transition);
    border-radius: var(--border-radius);
    margin: 2px 8px;
}

.pcoded-navbar .nav-link:hover {
    background: rgba(90, 155, 212, 0.1);
    transform: translateX(5px);
}

.pcoded-navbar .nav-link.active {
    background: linear-gradient(135deg, var(--primary-color), #4a90e2);
    color: white !important;
}

/* ===== Header ===== */
.pcoded-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    box-shadow: var(--box-shadow);
}

/* ===== Animations ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

/* ===== Utilitaires ===== */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), #4a90e2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color), #4a90e2) !important;
}

.shadow-soft {
    box-shadow: 0 2px 25px rgba(0,0,0,0.1) !important;
}

.border-radius {
    border-radius: var(--border-radius) !important;
}

/* ===== Responsive ===== */
@media (max-width: 768px) {
    .order-card {
        margin-bottom: 1rem;
    }
    
    .timeline {
        padding-left: 20px;
    }
    
    .timeline::before {
        left: 10px;
    }
    
    .timeline-marker {
        left: -17px;
    }
    
    .avatar-circle.large {
        width: 60px;
        height: 60px;
        font-size: 18px;
    }
    
    .avatar-circle.xl {
        width: 80px;
        height: 80px;
        font-size: 24px;
    }
}

/* ===== Dark Theme Support ===== */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #1a1a1a;
        --text-color: #ffffff;
        --card-bg: #2d2d2d;
    }
    
    .dark-theme .card {
        background-color: var(--card-bg);
        color: var(--text-color);
    }
    
    .dark-theme .table {
        background-color: var(--card-bg);
        color: var(--text-color);
    }
    
    .dark-theme .form-control {
        background-color: var(--card-bg);
        color: var(--text-color);
        border-color: #495057;
    }
}

/* ===== Print Styles ===== */
@media print {
    .no-print {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    .btn {
        display: none;
    }
}

/* ===== Loading States ===== */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* ===== Status Indicators ===== */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-indicator.online { background-color: var(--success-color); }
.status-indicator.offline { background-color: var(--danger-color); }
.status-indicator.away { background-color: var(--warning-color); }
.status-indicator.busy { background-color: var(--info-color); }

/* ===== Custom Scrollbar ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color), #4a90e2);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #4a90e2, var(--primary-color));
}
