/* Styles pour la page magasin */
.part-notes-row {
    background-color: #f9f9f9;
}

.part-notes-row td {
    padding-top: 0 !important;
}

/* Styles pour les sélecteurs de statut des pièces */
select.bg-danger, select.bg-warning, select.bg-success {
    color: white !important;
    font-weight: bold;
}

select.bg-danger option, select.bg-warning option, select.bg-success option {
    color: #333;
    font-weight: normal;
    background-color: white;
}

/* Animation pour les badges */
.badge {
    transition: all 0.3s ease;
}

.badge:hover {
    transform: scale(1.1);
}

/* Couleur violette pour les étapes en cours */
.bg-purple {
    background-color: #6f42c1 !important;
}

.text-purple {
    color: #6f42c1 !important;
}

/* Styles pour les sélecteurs de statut des pièces avec couleur violette */
select.bg-purple {
    color: white !important;
    font-weight: bold;
}

select.bg-purple option {
    color: #333;
    font-weight: normal;
    background-color: white;
}

/* Styles pour les boutons de suppression de photos */
.btn-delete-photo {
    position: absolute;
    top: 5px;
    right: 5px;
    z-index: 10;
    background-color: #ff3333;
    color: white;
    border: 2px solid white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    font-size: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    outline: none;
    opacity: 0.7;
    transition: opacity 0.3s ease, transform 0.2s ease, box-shadow 0.2s ease;
}

.btn-delete-photo:hover {
    background-color: #ff0000;
    opacity: 1;
    transform: scale(1.15);
    box-shadow: 0 3px 7px rgba(0, 0, 0, 0.3);
}

.btn-delete-photo:active {
    transform: scale(0.95);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}



.card:hover .btn-delete-photo {
    opacity: 1;
}
