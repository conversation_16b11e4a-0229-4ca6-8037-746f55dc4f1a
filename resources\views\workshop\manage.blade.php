@extends('layout')

@section('content')
<div class="pcoded-main-container">
    <div class="pcoded-content">
        <!-- [ breadcrumb ] start -->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10">Gestion des Étapes - RDV #{{ $rdv->id }}</h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}"><i class="feather icon-home"></i></a></li>
                            <li class="breadcrumb-item"><a href="{{ route('workshop.index') }}">Atelier</a></li>
                            <li class="breadcrumb-item"><a href="#!">Gestion des Étapes</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!-- [ breadcrumb ] end -->

        <!-- [ Main Content ] start -->
        <div class="row">
            <!-- [ Informations du RDV ] start -->
            <div class="col-md-12 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle mr-2"></i>Informations du Rendez-vous</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>Client:</strong><br>
                                @if($rdv->client)
                                    {{ $rdv->client->prenom }} {{ $rdv->client->nom }}
                                @else
                                    <span class="text-muted">Non défini</span>
                                @endif
                            </div>
                            <div class="col-md-3">
                                <strong>Véhicule:</strong><br>
                                @if($rdv->car)
                                    {{ $rdv->car->marque }} {{ $rdv->car->modele }}<br>
                                    <small class="text-muted">{{ $rdv->car->immatriculation }}</small>
                                @else
                                    <span class="text-muted">Non défini</span>
                                @endif
                            </div>
                            <div class="col-md-3">
                                <strong>Date RDV:</strong><br>
                                {{ $rdv->jour_rdv->format('d/m/Y') }} à {{ $rdv->heure_rdv->format('H:i') }}
                            </div>
                            <div class="col-md-3">
                                <strong>Statut:</strong><br>
                                @php
                                    $statusColor = $statusDefinitions[$rdv->statut]['color'] ?? 'secondary';
                                @endphp
                                <span class="badge badge-{{ $statusColor }}">{{ $rdv->formatted_status }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- [ Informations du RDV ] end -->

            <!-- [ Progression des Étapes ] start -->
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-tasks mr-2"></i>Progression des Étapes</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach($stages as $stage)
                                @php
                                    $isCurrentStage = $rdv->current_stage_id == $stage->id;
                                    $isCompleted = $rdv->current_stage_id > $stage->id;
                                    $stageColor = $stageDefinitions[$stage->id]['color'] ?? 'secondary';
                                    
                                    if ($isCompleted) {
                                        $cardClass = 'border-success';
                                        $iconClass = 'text-success';
                                        $icon = 'fas fa-check-circle';
                                    } elseif ($isCurrentStage) {
                                        $cardClass = 'border-primary';
                                        $iconClass = 'text-primary';
                                        $icon = 'fas fa-clock';
                                    } else {
                                        $cardClass = 'border-light';
                                        $iconClass = 'text-muted';
                                        $icon = 'fas fa-circle';
                                    }
                                @endphp
                                
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card {{ $cardClass }} h-100">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="{{ $icon }} {{ $iconClass }} mr-2"></i>
                                                <h6 class="mb-0">{{ $stage->name }}</h6>
                                            </div>
                                            
                                            @if($stage->description)
                                                <p class="text-muted small mb-2">{{ $stage->description }}</p>
                                            @endif
                                            
                                            @if($stage->estimated_duration)
                                                <p class="small mb-2">
                                                    <i class="fas fa-clock mr-1"></i>
                                                    Durée estimée: {{ $stage->formatted_duration }}
                                                </p>
                                            @endif
                                            
                                            @if($isCurrentStage)
                                                <div class="mt-3">
                                                    <button class="btn btn-sm btn-success" onclick="completeStage({{ $stage->id }})">
                                                        <i class="fas fa-check mr-1"></i>Terminer cette étape
                                                    </button>
                                                </div>
                                            @elseif($isCompleted)
                                                @php
                                                    $stageRecord = $rdv->rdvStages->where('stage_id', $stage->id)->first();
                                                @endphp
                                                @if($stageRecord)
                                                    <div class="mt-2">
                                                        <small class="text-success">
                                                            <i class="fas fa-check mr-1"></i>
                                                            Terminé le {{ $stageRecord->end_date ? $stageRecord->end_date->format('d/m/Y H:i') : 'Date inconnue' }}
                                                        </small>
                                                        @if($stageRecord->user)
                                                            <br><small class="text-muted">Par: {{ $stageRecord->user->prenom }} {{ $stageRecord->user->nom }}</small>
                                                        @endif
                                                    </div>
                                                @endif
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
            <!-- [ Progression des Étapes ] end -->

            <!-- [ Actions ] start -->
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cogs mr-2"></i>Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Gestion des Étapes</h6>
                                <button class="btn btn-primary mr-2" onclick="addNote()">
                                    <i class="fas fa-sticky-note mr-1"></i>Ajouter une Note
                                </button>
                                <button class="btn btn-info mr-2" onclick="uploadPhotos()">
                                    <i class="fas fa-camera mr-1"></i>Ajouter des Photos
                                </button>
                            </div>
                            <div class="col-md-6">
                                <h6>Actions Spéciales</h6>
                                @if($rdv->current_stage_id > 1)
                                    <button class="btn btn-warning mr-2" onclick="returnToPreviousStage()">
                                        <i class="fas fa-undo mr-1"></i>Retour Étape Précédente
                                    </button>
                                @endif
                                <button class="btn btn-success" onclick="markAsCompleted()">
                                    <i class="fas fa-check-circle mr-1"></i>Marquer comme Terminé
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- [ Actions ] end -->
        </div>
        <!-- [ Main Content ] end -->
    </div>
</div>

<!-- [ Modal Ajouter Note ] start -->
<div class="modal fade" id="noteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Ajouter une Note</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="noteForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="note_text">Note</label>
                        <textarea class="form-control" id="note_text" name="note_text" rows="4" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Ajouter</button>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- [ Modal Ajouter Note ] end -->

@endsection

@push('scripts')
<script>
function completeStage(stageId) {
    if (confirm('Êtes-vous sûr de vouloir terminer cette étape ?')) {
        $.ajax({
            url: '/atelier/complete-stage',
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                rdv_id: {{ $rdv->id }},
                stage_id: stageId
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('Erreur: ' + response.message);
                }
            },
            error: function(xhr) {
                alert('Erreur lors de la mise à jour de l\'étape');
            }
        });
    }
}

function returnToPreviousStage() {
    if (confirm('Êtes-vous sûr de vouloir revenir à l\'étape précédente ?')) {
        $.ajax({
            url: '/atelier/return-to-previous',
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                rdv_id: {{ $rdv->id }}
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('Erreur: ' + response.message);
                }
            },
            error: function(xhr) {
                alert('Erreur lors du retour à l\'étape précédente');
            }
        });
    }
}

function markAsCompleted() {
    if (confirm('Êtes-vous sûr de vouloir marquer ce RDV comme terminé ?')) {
        $.ajax({
            url: '/atelier/mark-completed',
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                rdv_id: {{ $rdv->id }}
            },
            success: function(response) {
                if (response.success) {
                    alert('RDV marqué comme terminé avec succès');
                    window.location.href = '{{ route("workshop.index") }}';
                } else {
                    alert('Erreur: ' + response.message);
                }
            },
            error: function(xhr) {
                alert('Erreur lors de la finalisation du RDV');
            }
        });
    }
}

function addNote() {
    $('#noteModal').modal('show');
}

function uploadPhotos() {
    // Rediriger vers la page d'upload de photos ou ouvrir un modal
    alert('Fonctionnalité d\'upload de photos à implémenter');
}

$('#noteForm').submit(function(e) {
    e.preventDefault();
    
    $.ajax({
        url: '/atelier/add-note',
        method: 'POST',
        data: {
            _token: '{{ csrf_token() }}',
            rdv_id: {{ $rdv->id }},
            note: $('#note_text').val()
        },
        success: function(response) {
            if (response.success) {
                $('#noteModal').modal('hide');
                $('#note_text').val('');
                alert('Note ajoutée avec succès');
                location.reload();
            } else {
                alert('Erreur: ' + response.message);
            }
        },
        error: function(xhr) {
            alert('Erreur lors de l\'ajout de la note');
        }
    });
});
</script>
@endpush
