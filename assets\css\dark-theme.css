/* Styles pour le mode sombre global */

body.dark-mode {
    background-color: #222;
    color: #f8f9fa;
}

/* Navbar et sidebar */
body.dark-mode .pcoded-header {
    background-color: #333;
    color: #f8f9fa;
}

body.dark-mode .pcoded-navbar {
    background-color: #333;
}

body.dark-mode .pcoded-navbar .pcoded-inner-navbar li.active > a, 
body.dark-mode .pcoded-navbar .pcoded-inner-navbar li:focus > a, 
body.dark-mode .pcoded-navbar .pcoded-inner-navbar li:hover > a {
    color: #fff;
}

body.dark-mode .pcoded-navbar .pcoded-inner-navbar li.pcoded-menu-caption {
    color: #adb5bd;
}

body.dark-mode .pcoded-navbar .pcoded-inner-navbar li > a {
    color: #dee2e6;
}

/* Cartes et conteneurs */
body.dark-mode .card {
    background-color: #333;
    border-color: #444;
}

body.dark-mode .card-header {
    background-color: #444;
    border-color: #555;
}

body.dark-mode .table {
    color: #f8f9fa;
}

body.dark-mode .table thead th {
    border-color: #444;
}

body.dark-mode .table td {
    border-color: #444;
}

/* Formulaires */
body.dark-mode .form-control {
    background-color: #444;
    border-color: #555;
    color: #f8f9fa;
}

body.dark-mode .form-control:focus {
    background-color: #444;
    color: #f8f9fa;
}

body.dark-mode .custom-file-label {
    background-color: #444;
    border-color: #555;
    color: #f8f9fa;
}

body.dark-mode .input-group-text {
    background-color: #555;
    border-color: #666;
    color: #f8f9fa;
}

/* Modals */
body.dark-mode .modal-content {
    background-color: #333;
    border-color: #444;
}

body.dark-mode .modal-header,
body.dark-mode .modal-footer {
    border-color: #444;
}

body.dark-mode .close {
    color: #f8f9fa;
}

/* Textes et liens */
body.dark-mode .text-muted {
    color: #adb5bd !important;
}

body.dark-mode a:not(.btn) {
    color: #4680ff;
}

body.dark-mode a:not(.btn):hover {
    color: #6c9fff;
}

/* Onglets */
body.dark-mode .nav-tabs {
    border-color: #444;
}

body.dark-mode .nav-tabs .nav-link {
    color: #adb5bd;
}

body.dark-mode .nav-tabs .nav-link.active {
    color: #4680ff;
    background-color: #333;
    border-color: #444 #444 #333;
}

/* Dropdown menus */
body.dark-mode .dropdown-menu {
    background-color: #333;
    border-color: #444;
}

body.dark-mode .dropdown-item {
    color: #f8f9fa;
}

body.dark-mode .dropdown-item:hover,
body.dark-mode .dropdown-item:focus {
    background-color: #444;
    color: #fff;
}

body.dark-mode .dropdown-divider {
    border-color: #444;
}

/* Pagination */
body.dark-mode .page-item .page-link {
    background-color: #333;
    border-color: #444;
    color: #f8f9fa;
}

body.dark-mode .page-item.active .page-link {
    background-color: #4680ff;
    border-color: #4680ff;
}

body.dark-mode .page-item.disabled .page-link {
    background-color: #333;
    border-color: #444;
    color: #6c757d;
}

/* Alertes */
body.dark-mode .alert-success {
    background-color: #285b2a;
    border-color: #1e441f;
    color: #d4edda;
}

body.dark-mode .alert-danger {
    background-color: #6b2025;
    border-color: #58181c;
    color: #f8d7da;
}

body.dark-mode .alert-warning {
    background-color: #664d03;
    border-color: #513e02;
    color: #fff3cd;
}

body.dark-mode .alert-info {
    background-color: #055160;
    border-color: #04414d;
    color: #cff4fc;
}

/* Badges */
body.dark-mode .badge-success {
    background-color: #28a745;
}

body.dark-mode .badge-danger {
    background-color: #dc3545;
}

body.dark-mode .badge-warning {
    background-color: #ffc107;
    color: #212529;
}

body.dark-mode .badge-info {
    background-color: #17a2b8;
}

/* Breadcrumb */
body.dark-mode .breadcrumb {
    background-color: #444;
}

body.dark-mode .breadcrumb-item.active {
    color: #adb5bd;
}

/* Boutons */
body.dark-mode .btn-secondary {
    background-color: #555;
    border-color: #666;
}

body.dark-mode .btn-secondary:hover {
    background-color: #666;
    border-color: #777;
}

body.dark-mode .btn-light {
    background-color: #444;
    border-color: #555;
    color: #f8f9fa;
}

body.dark-mode .btn-light:hover {
    background-color: #555;
    border-color: #666;
    color: #fff;
}

/* DataTables */
body.dark-mode .dataTables_wrapper .dataTables_length,
body.dark-mode .dataTables_wrapper .dataTables_filter,
body.dark-mode .dataTables_wrapper .dataTables_info,
body.dark-mode .dataTables_wrapper .dataTables_processing,
body.dark-mode .dataTables_wrapper .dataTables_paginate {
    color: #f8f9fa;
}

body.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button {
    color: #f8f9fa !important;
}

body.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button.current,
body.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    background: #4680ff;
    border-color: #4680ff;
    color: #fff !important;
}

body.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: #444;
    border-color: #555;
    color: #fff !important;
}
