<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class RequiredPart extends Model
{
    use HasFactory;

    protected $table = 'required_parts';
    protected $guarded = [];

    protected $fillable = [
        'damage_assessment_id',
        'rdv_id',
        'part_name',
        'part_number',
        'quantity',
        'unit_price',
        'total_price',
        'supplier',
        'status',
        'ordered_date',
        'received_date',
        'notes'
    ];

    protected $casts = [
        'quantity' => 'integer',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'ordered_date' => 'date',
        'received_date' => 'date'
    ];

    /**
     * Relation avec l'évaluation de dommage
     */
    public function damageAssessment()
    {
        return $this->belongsTo(DamageAssessment::class, 'damage_assessment_id');
    }

    /**
     * Relation avec le rendez-vous
     */
    public function rdv()
    {
        return $this->belongsTo(Rdv::class, 'rdv_id');
    }

    /**
     * Obtenir le statut formaté
     */
    public function getFormattedStatusAttribute()
    {
        $statuses = [
            'needed' => 'Nécessaire',
            'ordered' => 'Commandé',
            'received' => 'Reçu',
            'installed' => 'Installé',
            'cancelled' => 'Annulé'
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * Calculer le prix total automatiquement
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($part) {
            $part->total_price = $part->quantity * $part->unit_price;
        });
    }

    /**
     * Scope pour les pièces commandées
     */
    public function scopeOrdered($query)
    {
        return $query->where('status', 'ordered');
    }

    /**
     * Scope pour les pièces reçues
     */
    public function scopeReceived($query)
    {
        return $query->where('status', 'received');
    }

    /**
     * Scope pour les pièces installées
     */
    public function scopeInstalled($query)
    {
        return $query->where('status', 'installed');
    }
}
