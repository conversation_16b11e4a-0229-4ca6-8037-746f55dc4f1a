<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Rdv;
use App\Models\Stage;
use App\Models\StagePhoto;
use App\Models\DamageAssessment;
use App\Models\RequiredPart;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class AtelierController extends Controller
{
    public function index(Request $request)
    {
        // Vérifier l'authentification
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        // Récupérer les paramètres de filtrage
        $stageFilter = $request->input('stage', 'all');
        $date = $request->input('date');
        $search = $request->input('search');

        // Requête de base
        $query = Rdv::with(['car.client', 'currentStage', 'latestStageStatus'])
            ->where('current_stage_id', '>', 1)
            ->where(function($q) {
                $q->where('statut', 'confirme')
                  ->orWhere('statut', 'en_cours');
            });

        // Appliquer les filtres
        if ($stageFilter !== 'all') {
            $query->where('current_stage_id', $stageFilter);
        }

        if ($date) {
            $query->where('jour_rdv', $date);
        }

        if ($search) {
            $query->whereHas('car', function($q) use ($search) {
                $q->where('immatriculation', 'like', "%$search%")
                  ->orWhere('marque', 'like', "%$search%")
                  ->orWhere('modele', 'like', "%$search%");
            })->orWhereHas('car.client', function($q) use ($search) {
                $q->where('nom', 'like', "%$search%")
                  ->orWhere('prenom', 'like', "%$search%")
                  ->orWhere('telephone', 'like', "%$search%");
            });
        }

        // Trier les résultats
        $missions = $query->orderBy('jour_rdv', 'desc')
                         ->orderBy('heure_rdv', 'desc')
                         ->get();

        // Récupérer toutes les étapes pour le filtre
        $stages = Stage::whereNotIn('id', [1])
                      ->orderBy('order_num')
                      ->get();

        return view('workshop.index', [
            'missions' => $missions,
            'stages' => $stages,
            'filters' => [
                'stage' => $stageFilter,
                'date' => $date,
                'search' => $search
            ],
            'stageDefinitions' => $this->getStageDefinitions(),
            'statusDefinitions' => $this->getStatusDefinitions()
        ]);
    }

    public function details($id)
    {
        $rdv = Rdv::with([
            'car.client',
            'currentStage',
            'stagePhotos',
            'damageAssessments.requiredParts',
            'rdvStages' => function($query) {
                $query->orderBy('created_at', 'desc');
            }
        ])->findOrFail($id);

        return view('workshop._details', [
            'rdv' => $rdv,
            'stageDefinitions' => $this->getStageDefinitions(),
            'statusDefinitions' => $this->getStatusDefinitions()
        ]);
    }

    public function deletePhoto(Request $request)
    {
        $request->validate([
            'photo_id' => 'required|integer'
        ]);

        try {
            $photo = StagePhoto::findOrFail($request->photo_id);

            // Vérifier les permissions si nécessaire
            // if ($photo->uploaded_by != Auth::id()) { ... }

            // Supprimer le fichier physique
            if (Storage::exists($photo->file_path)) {
                Storage::delete($photo->file_path);
            }

            // Supprimer de la base de données
            $photo->delete();

            return response()->json([
                'success' => true,
                'message' => 'Photo supprimée avec succès'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur: ' . $e->getMessage()
            ], 500);
        }
    }

    public function returnToRepair(Request $request)
    {
        $request->validate([
            'rdv_id' => 'required|integer',
            'stage_id' => 'required|integer',
            'return_reason' => 'required|string'
        ]);

        return DB::transaction(function () use ($request) {
            $rdv = Rdv::findOrFail($request->rdv_id);

            // Vérifier que le rendez-vous est bien à l'étape de contrôle qualité (6)
            if ($rdv->current_stage_id != 6) {
                return redirect()->route('workshop.index')
                    ->with('error', 'Le rendez-vous n\'est pas à l\'étape de contrôle qualité');
            }

            // Mettre à jour le statut du rendez-vous
            $rdv->current_stage_id = 5; // Retour à l'étape de réparation
            $rdv->save();

            // Mettre à jour l'étape de contrôle qualité
            $rdv->rdvStages()
                ->where('stage_id', 6)
                ->latest()
                ->first()
                ->update([
                    'notes' => DB::raw("CONCAT(IFNULL(notes, ''), '\nRetour à l'étape de réparation le ', NOW(), ': ', {$request->return_reason}"),
                    'end_date' => null
                ]);

            // Créer une nouvelle entrée pour l'étape de réparation
            $rdv->rdvStages()->create([
                'stage_id' => 5,
                'status' => 'en_attente',
                'start_date' => now(),
                'user_id' => Auth::id(),
                'notes' => "Retour de l'étape de contrôle qualité. Raison: " . $request->return_reason
            ]);

            return redirect()->route('workshop.index')
                ->with('success', 'Véhicule renvoyé à l\'étape de réparation avec succès');
        });
    }

    public function uploadPhotos(Request $request)
    {
        $request->validate([
            'rdv_id' => 'required|integer',
            'photos.*' => 'image|mimes:jpeg,png,jpg|max:5120',
            'notes' => 'nullable|string'
        ]);

        try {
            $rdv = Rdv::findOrFail($request->rdv_id);

            // Vérifier le statut du rendez-vous
            if ($rdv->statut !== 'confirme') {
                return redirect()->route('workshop.index')
                    ->with('error', 'Le rendez-vous n\'est pas dans l\'état "confirme"');
            }

            // Vérifier si des fichiers ont été téléchargés
            if (!$request->hasFile('photos')) {
                return redirect()->route('workshop.index')
                    ->with('error', 'Aucune photo téléchargée');
            }

            // Créer le dossier de stockage
            $uploadDir = "car_photos/{$rdv->id}";
            $uploadedFiles = [];

            foreach ($request->file('photos') as $file) {
                $path = $file->store($uploadDir, 'public');

                $photo = StagePhoto::create([
                    'rdv_id' => $rdv->id,
                    'stage_id' => 2, // Étape de photographie avant réparation
                    'file_path' => $path,
                    'file_name' => $file->getClientOriginalName(),
                    'uploaded_by' => Auth::id(),
                    'notes' => $request->notes
                ]);

                $uploadedFiles[] = $photo;
            }

            return redirect()->route('workshop.index')
                ->with('success', count($uploadedFiles) . ' photo(s) téléchargée(s) avec succès');
        } catch (\Exception $e) {
            return redirect()->route('workshop.index')
                ->with('error', 'Erreur: ' . $e->getMessage());
        }
    }

    public function completeStage(Request $request)
    {
        $request->validate([
            'rdv_id' => 'required|integer',
            'stage_id' => 'required|integer',
            'stage_status' => 'sometimes|in:en_attente,confirme'
        ]);

        return DB::transaction(function () use ($request) {
            $rdv = Rdv::findOrFail($request->rdv_id);
            $stageId = $request->stage_id;
            $stageStatus = $request->input('stage_status', 'confirme');
            $userId = Auth::id();

            switch ($stageId) {
                case 2: // Photographie avant réparation
                    // Vérifier si des photos existent
                    if (!StagePhoto::where('rdv_id', $rdv->id)->where('stage_id', 2)->exists()) {
                        return redirect()->route('workshop.index')
                            ->with('error', 'Aucune photo n\'a été téléchargée pour ce rendez-vous');
                    }

                    // Mettre à jour le statut du rendez-vous
                    $rdv->current_stage_id = 3;
                    $rdv->save();

                    // Enregistrer la complétion de l'étape
                    $rdv->rdvStages()->create([
                        'stage_id' => 2,
                        'status' => $stageStatus,
                        'start_date' => now(),
                        'end_date' => now(),
                        'user_id' => $userId,
                        'notes' => 'Étape complétée'
                    ]);

                    // Initialiser l'étape suivante
                    $rdv->rdvStages()->create([
                        'stage_id' => 3,
                        'status' => 'en_cours',
                        'start_date' => now(),
                        'user_id' => $userId
                    ]);

                    return redirect()->route('workshop.index')
                        ->with('success', 'Étape de photographie avant réparation terminée avec succès');

                // ... autres cas (similaires à la logique originale)
                
                default:
                    return redirect()->route('workshop.index')
                        ->with('error', 'Étape non reconnue');
            }
        });
    }

    private function getStageDefinitions()
    {
        return [
            1 => ['name' => 'Rendez-vous', 'color' => 'secondary'],
            2 => ['name' => 'Photographie avant réparation', 'color' => 'purple'],
            3 => ['name' => 'Démontage et évaluation', 'color' => 'primary'],
            4 => ['name' => 'Attente pièces détachées', 'color' => 'warning'],
            5 => ['name' => 'Installation et réparation', 'color' => 'primary'],
            6 => ['name' => 'Contrôle qualité', 'color' => 'info'],
            7 => ['name' => 'Nettoyage et préparation', 'color' => 'info'],
            8 => ['name' => 'Photographie après réparation', 'color' => 'info'],
            9 => ['name' => 'Facturation', 'color' => 'warning'],
            10 => ['name' => 'Livraison', 'color' => 'success']
        ];
    }

    private function getStatusDefinitions()
    {
        return [
            'en_attente' => ['label' => 'En attente', 'color' => 'warning'],
            'confirme' => ['label' => 'Confirmé', 'color' => 'info'],
            'annule' => ['label' => 'Annulé', 'color' => 'danger']
        ];
    }
}