<!DOCTYPE html>
<html lang="fr">

<head>

    <title>AutoFix Pro</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="description" content="" />
    <meta name="keywords" content="">
    <meta name="author" content="Phoenixcoded" />
    <!-- Favicon icon -->
    <link rel="icon" href="assets/images/favicon.ico" type="image/x-icon">

    <!-- vendor css -->
    <link rel="stylesheet" href="assets/css/style.css">

</head>

<body>
    <!-- [ auth-signin ] start -->
    <div class="auth-wrapper">
        <div class="auth-content">
            <div class="card">
                <div class="row align-items-center text-center">
                    <div class="col-md-12">
                        <div class="card-body">
                            <img src="assets/images/logo-dark.png" alt="" class="img-fluid mb-3" width="130">
                            <h4 class="mb-4 f-w-400">Connexion</h4>
                            @if ($errors->any())
                                <h4 style="color: red;">
                                    {{ $errors->first() }}
                                </h4>
                            @endif
                            <form method="POST" action="{{ route('login.submit') }}">
                                @csrf
                                <div class="form-group mb-3">
                                    <label class="floating-label" for="Email">Adresse Email</label>
                                    <input type="email" class="form-control" name="email" id="Email"
                                        placeholder="Adresse Email">
                                </div>
                                <div class="form-group mb-4">
                                    <label class="floating-label" for="Password">Mot de passe</label>
                                    <input type="password" class="form-control" name="password" id="Password"
                                        placeholder="Mot de passe">
                                </div>
                                <button type="submit" name="login"
                                    class="btn btn-block btn-primary mb-2">Connecter</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/vendor-all.min.js"></script>
    <script src="assets/js/plugins/bootstrap.min.js"></script>
    <script src="assets/js/ripple.js"></script>
    <script src="assets/js/pcoded.min.js"></script>

</body>

</html>
