/* Styles pour la barre de progression des étapes */
.progress-container {
    width: 100%;
    margin: 20px auto;
    position: relative;
}

.progressbar {
    counter-reset: step;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.progressbar li {
    list-style-type: none;
    width: 20%;
    float: left;
    font-size: 14px;
    position: relative;
    text-align: center;
    text-transform: uppercase;
    color: #7d7d7d;
    margin-bottom: 30px;
    font-weight: 500;
    padding: 0 10px;
}

.progressbar li:before {
    width: 40px;
    height: 40px;
    content: counter(step);
    counter-increment: step;
    line-height: 40px;
    border: 2px solid #7d7d7d;
    display: block;
    text-align: center;
    margin: 0 auto 10px auto;
    border-radius: 50%;
    background-color: white;
    z-index: 2;
    position: relative;
}

.progressbar li:after {
    width: 100%;
    height: 2px;
    content: '';
    position: absolute;
    background-color: #7d7d7d;
    top: 20px;
    left: -50%;
    z-index: 1;
}

.progressbar li:first-child:after {
    content: none;
}

/* Étapes terminées */
.progressbar li.completed {
    color: #28a745;
}

.progressbar li.completed:before {
    border-color: #28a745;
    background-color: #28a745;
    color: white;
}

.progressbar li.completed:after {
    background-color: #28a745;
}

/* Étape actuelle */
.progressbar li.active {
    color: #007bff;
}

.progressbar li.active:before {
    border-color: #007bff;
    background-color: white;
    color: #007bff;
}

.progressbar li.active.current:before {
    background-color: #007bff;
    color: white;
}

.progressbar li.active:after {
    background-color: #007bff;
}

/* Étapes en attente */
.progressbar li.pending {
    color: #6c757d;
}

.progressbar li.pending:before {
    border-color: #6c757d;
    background-color: white;
    color: #6c757d;
}

/* Badges d'état des étapes */
.stage-badge {
    display: inline-block;
    margin-left: 5px;
    font-size: 12px;
}

.stage-badge.completed {
    color: #28a745;
}

.stage-badge.current {
    color: #007bff;
}

.stage-badge.pending {
    color: #6c757d;
}

/* Responsive design */
@media (max-width: 992px) {
    .progressbar li {
        width: 33.33%;
    }
}

@media (max-width: 768px) {
    .progressbar li {
        width: 50%;
    }
}

@media (max-width: 576px) {
    .progressbar li {
        width: 100%;
        margin-bottom: 20px;
    }
    
    .progressbar li:after {
        display: none;
    }
}
