<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cars', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('client_id');
            $table->string('marque', 50);
            $table->string('modele', 50);
            $table->year('annee');
            $table->string('immatriculation', 20)->unique();
            $table->string('vin', 50)->nullable();
            $table->string('couleur', 30)->nullable();
            $table->integer('kilometrage')->nullable();
            $table->enum('carburant', ['essence', 'diesel', 'hybride', 'electrique', 'gpl'])->nullable();
            $table->enum('transmission', ['manuelle', 'automatique'])->nullable();
            $table->text('notes')->nullable();
            $table->boolean('active')->default(true);
            $table->timestamps();
            
            $table->foreign('client_id')->references('id')->on('clients')->onDelete('cascade');
            $table->index(['marque', 'modele']);
            $table->index('immatriculation');
            $table->index('vin');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cars');
    }
};
