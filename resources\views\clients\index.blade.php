@extends('layout')

@section('content')
<div class="pcoded-main-container">
    <div class="pcoded-content">
        <!-- [ breadcrumb ] start -->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10">Gestion des Clients</h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}"><i class="feather icon-home"></i></a></li>
                            <li class="breadcrumb-item"><a href="#!">Clients</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!-- [ breadcrumb ] end -->

        <!-- [ Main Content ] start -->
        <div class="row">
            <!-- [ Statistiques ] start -->
            <div class="col-md-6 col-xl-3">
                <div class="card bg-c-blue order-card">
                    <div class="card-body">
                        <h6 class="text-white">Total Clients</h6>
                        <h2 class="text-white">{{ $stats['total'] }}</h2>
                        <p class="m-b-0">Clients enregistrés</p>
                        <i class="fas fa-users f-right f-26 text-c-blue"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-xl-3">
                <div class="card bg-c-green order-card">
                    <div class="card-body">
                        <h6 class="text-white">Clients Actifs</h6>
                        <h2 class="text-white">{{ $stats['active'] }}</h2>
                        <p class="m-b-0">Comptes actifs</p>
                        <i class="fas fa-user-check f-right f-26 text-c-green"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-xl-3">
                <div class="card bg-c-yellow order-card">
                    <div class="card-body">
                        <h6 class="text-white">Avec Véhicules</h6>
                        <h2 class="text-white">{{ $stats['with_cars'] }}</h2>
                        <p class="m-b-0">Ont des véhicules</p>
                        <i class="fas fa-car f-right f-26 text-c-yellow"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-xl-3">
                <div class="card bg-c-red order-card">
                    <div class="card-body">
                        <h6 class="text-white">Inactifs</h6>
                        <h2 class="text-white">{{ $stats['inactive'] }}</h2>
                        <p class="m-b-0">Comptes inactifs</p>
                        <i class="fas fa-user-times f-right f-26 text-c-red"></i>
                    </div>
                </div>
            </div>
            <!-- [ Statistiques ] end -->

            <!-- [ Filtres et Actions ] start -->
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-filter mr-2"></i>Filtres et Actions</h5>
                        <div class="card-header-right">
                            <a href="{{ route('clients.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus mr-2"></i>Nouveau Client
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="{{ route('clients.index') }}">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="search">Recherche</label>
                                    <input type="text" class="form-control" id="search" name="search" 
                                           placeholder="Nom, email, téléphone..." value="{{ $search }}">
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="type">Type de Client</label>
                                    <select class="form-control" id="type" name="type">
                                        <option value="all" {{ $type === 'all' ? 'selected' : '' }}>Tous les types</option>
                                        <option value="particulier" {{ $type === 'particulier' ? 'selected' : '' }}>Particulier</option>
                                        <option value="professionnel" {{ $type === 'professionnel' ? 'selected' : '' }}>Professionnel</option>
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="status">Statut</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="all" {{ $status === 'all' ? 'selected' : '' }}>Tous les statuts</option>
                                        <option value="active" {{ $status === 'active' ? 'selected' : '' }}>Actifs</option>
                                        <option value="inactive" {{ $status === 'inactive' ? 'selected' : '' }}>Inactifs</option>
                                    </select>
                                </div>
                                <div class="col-md-2 mb-3 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary mr-2">
                                        <i class="fas fa-search"></i> Filtrer
                                    </button>
                                    <a href="{{ route('clients.index') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-undo"></i>
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- [ Filtres et Actions ] end -->

            <!-- [ Liste des clients ] start -->
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list mr-2"></i>Liste des Clients ({{ $clients->total() }})</h5>
                    </div>
                    <div class="card-body">
                        @if($clients->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Client</th>
                                            <th>Contact</th>
                                            <th>Type</th>
                                            <th>Véhicules</th>
                                            <th>Dernière Visite</th>
                                            <th>Statut</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($clients as $client)
                                            <tr>
                                                <td><strong>#{{ $client->id }}</strong></td>
                                                <td>
                                                    <div>
                                                        <strong>{{ $client->full_name }}</strong>
                                                        @if($client->date_naissance)
                                                            <br><small class="text-muted">
                                                                Né(e) le {{ $client->date_naissance->format('d/m/Y') }}
                                                            </small>
                                                        @endif
                                                    </div>
                                                </td>
                                                <td>
                                                    <div>
                                                        @if($client->email)
                                                            <i class="fas fa-envelope mr-1"></i>{{ $client->email }}<br>
                                                        @endif
                                                        @if($client->telephone)
                                                            <i class="fas fa-phone mr-1"></i>{{ $client->telephone }}
                                                        @endif
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge badge-{{ $client->type_client === 'professionnel' ? 'primary' : 'info' }}">
                                                        {{ ucfirst($client->type_client) }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge badge-secondary">
                                                        {{ $client->cars->count() }} véhicule(s)
                                                    </span>
                                                </td>
                                                <td>
                                                    @php
                                                        $lastRdv = $client->rdvs()->latest()->first();
                                                    @endphp
                                                    @if($lastRdv)
                                                        {{ $lastRdv->jour_rdv->format('d/m/Y') }}
                                                    @else
                                                        <span class="text-muted">Jamais</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <span class="badge badge-{{ $client->active ? 'success' : 'danger' }}">
                                                        {{ $client->active ? 'Actif' : 'Inactif' }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="{{ route('clients.show', $client) }}" 
                                                           class="btn btn-sm btn-primary">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="{{ route('clients.edit', $client) }}" 
                                                           class="btn btn-sm btn-warning">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <button type="button" class="btn btn-sm btn-{{ $client->active ? 'secondary' : 'success' }}"
                                                                onclick="toggleClientStatus({{ $client->id }})">
                                                            <i class="fas fa-{{ $client->active ? 'ban' : 'check' }}"></i>
                                                        </button>
                                                        @if($client->rdvs()->count() === 0)
                                                            <button type="button" class="btn btn-sm btn-danger"
                                                                    onclick="deleteClient({{ $client->id }})">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        @endif
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <div class="d-flex justify-content-center">
                                {{ $clients->appends(request()->query())->links() }}
                            </div>
                        @else
                            <div class="text-center py-5">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">Aucun client trouvé</h5>
                                <p class="text-muted">Il n'y a aucun client correspondant aux critères sélectionnés.</p>
                                <a href="{{ route('clients.create') }}" class="btn btn-primary">
                                    <i class="fas fa-plus mr-2"></i>Créer le premier client
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
            <!-- [ Liste des clients ] end -->
        </div>
        <!-- [ Main Content ] end -->
    </div>
</div>

@endsection

@push('scripts')
<script>
function toggleClientStatus(clientId) {
    if (confirm('Êtes-vous sûr de vouloir changer le statut de ce client ?')) {
        $.ajax({
            url: "{{ route('clients.toggleStatus', '') }}/" + clientId,
            method: 'POST',
            data: {
                _token: "{{ csrf_token() }}"
            },
            success: function(response) {
                location.reload();
            },
            error: function(xhr) {
                alert('Erreur: ' + xhr.responseJSON.message);
            }
        });
    }
}

function deleteClient(clientId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce client ? Cette action est irréversible.')) {
        $.ajax({
            url: "{{ route('clients.destroy', '') }}/" + clientId,
            method: 'DELETE',
            data: {
                _token: "{{ csrf_token() }}"
            },
            success: function(response) {
                location.reload();
            },
            error: function(xhr) {
                alert('Erreur: ' + xhr.responseJSON.message);
            }
        });
    }
}
</script>
@endpush
