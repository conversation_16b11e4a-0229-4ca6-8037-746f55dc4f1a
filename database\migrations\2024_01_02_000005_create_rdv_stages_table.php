<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('rdv_stages', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('rdv_id');
            $table->unsignedInteger('stage_id');
            $table->enum('status', ['en_attente', 'en_cours', 'termine', 'suspendu', 'annule'])->default('en_attente');
            $table->datetime('start_date')->nullable();
            $table->datetime('end_date')->nullable();
            $table->unsignedInteger('user_id')->nullable();
            $table->text('notes')->nullable();
            $table->integer('estimated_duration')->nullable(); // en minutes
            $table->integer('actual_duration')->nullable(); // en minutes
            $table->timestamps();
            
            $table->foreign('rdv_id')->references('id')->on('rdv')->onDelete('cascade');
            $table->foreign('stage_id')->references('id')->on('stages');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            
            $table->index(['rdv_id', 'stage_id']);
            $table->index('status');
            $table->index('start_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rdv_stages');
    }
};
