<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rules\Password;

class ProfileController extends Controller
{
    /**
     * Afficher le profil de l'utilisateur
     */
    public function index()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();

        // Statistiques de l'utilisateur
        $stats = $this->getUserStats($user);

        return view('profile.index', compact('user', 'stats'));
    }

    /**
     * Mettre à jour les informations du profil
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'prenom' => 'required|string|max:100',
            'nom' => 'required|string|max:100',
            'email' => 'required|email|unique:users,email,' . $user->id,
            'telephone' => 'nullable|string|max:20',
            'theme_preference' => 'required|in:light,dark,system'
        ]);

        $user->update([
            'prenom' => $request->prenom,
            'nom' => $request->nom,
            'email' => $request->email,
            'telephone' => $request->telephone,
            'theme_preference' => $request->theme_preference
        ]);

        return redirect()->route('profile.index')
            ->with('success', 'Profil mis à jour avec succès');
    }

    /**
     * Mettre à jour le mot de passe
     */
    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required',
            'password' => ['required', 'confirmed', Password::min(8)],
        ]);

        $user = Auth::user();

        // Vérifier le mot de passe actuel
        if (!Hash::check($request->current_password, $user->password)) {
            return back()->withErrors([
                'current_password' => 'Le mot de passe actuel est incorrect'
            ]);
        }

        $user->update([
            'password' => Hash::make($request->password)
        ]);

        return redirect()->route('profile.index')
            ->with('success', 'Mot de passe mis à jour avec succès');
    }

    /**
     * Mettre à jour la photo de profil
     */
    public function updateAvatar(Request $request)
    {
        $request->validate([
            'avatar' => 'required|image|mimes:jpeg,png,jpg|max:2048'
        ]);

        $user = Auth::user();

        // Supprimer l'ancienne photo si elle existe
        if ($user->avatar && Storage::disk('public')->exists($user->avatar)) {
            Storage::disk('public')->delete($user->avatar);
        }

        // Sauvegarder la nouvelle photo
        $path = $request->file('avatar')->store('avatars', 'public');

        $user->update(['avatar' => $path]);

        return redirect()->route('profile.index')
            ->with('success', 'Photo de profil mise à jour avec succès');
    }

    /**
     * Supprimer la photo de profil
     */
    public function deleteAvatar()
    {
        $user = Auth::user();

        if ($user->avatar && Storage::disk('public')->exists($user->avatar)) {
            Storage::disk('public')->delete($user->avatar);
            $user->update(['avatar' => null]);
        }

        return redirect()->route('profile.index')
            ->with('success', 'Photo de profil supprimée avec succès');
    }

    /**
     * Obtenir les statistiques de l'utilisateur
     */
    private function getUserStats($user)
    {
        $stats = [
            'rdv_assignes' => 0,
            'rdv_termines' => 0,
            'rdv_en_cours' => 0,
            'temps_total' => 0,
            'derniere_connexion' => $user->last_login,
            'membre_depuis' => $user->created_at
        ];

        // Si c'est un technicien, calculer ses statistiques
        if ($user->role === 'technicien') {
            $stats['rdv_assignes'] = \App\Models\Rdv::where('assigned_to', $user->id)->count();
            $stats['rdv_termines'] = \App\Models\Rdv::where('assigned_to', $user->id)
                ->where('statut', 'termine')->count();
            $stats['rdv_en_cours'] = \App\Models\Rdv::where('assigned_to', $user->id)
                ->where('statut', 'en_cours')->count();

            // Temps total de travail
            $stats['temps_total'] = \Illuminate\Support\Facades\DB::table('rdv_stages')
                ->where('user_id', $user->id)
                ->whereNotNull('actual_duration')
                ->sum('actual_duration');
        }

        return $stats;
    }

    /**
     * Obtenir l'historique d'activité de l'utilisateur
     */
    public function getActivity(Request $request)
    {
        $user = Auth::user();
        $limit = $request->input('limit', 10);

        $activities = [];

        // Si c'est un technicien, récupérer ses activités
        if ($user->role === 'technicien') {
            // Dernières étapes travaillées
            $recentStages = \App\Models\RdvStage::with(['rdv.client', 'rdv.car', 'stage'])
                ->where('user_id', $user->id)
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->get();

            foreach ($recentStages as $stage) {
                $clientName = 'Client inconnu';
                if ($stage->rdv && $stage->rdv->client) {
                    $clientName = 'Client: ' . $stage->rdv->client->prenom . ' ' . $stage->rdv->client->nom;
                }

                $activities[] = [
                    'type' => 'stage',
                    'title' => 'Étape: ' . ($stage->stage->name ?? 'Inconnue'),
                    'description' => $clientName,
                    'date' => $stage->created_at,
                    'status' => $stage->status,
                    'icon' => 'fas fa-tasks',
                    'color' => $stage->status === 'termine' ? 'success' : 'primary'
                ];
            }
        }

        // Trier par date
        usort($activities, function($a, $b) {
            return $b['date'] <=> $a['date'];
        });

        return response()->json(array_slice($activities, 0, $limit));
    }

    /**
     * Mettre à jour les préférences de notification
     */
    public function updateNotifications(Request $request)
    {
        $user = Auth::user();

        $notifications = [
            'email_rdv' => $request->has('email_rdv'),
            'email_stage' => $request->has('email_stage'),
            'email_rappel' => $request->has('email_rappel'),
            'sms_rdv' => $request->has('sms_rdv'),
            'sms_urgent' => $request->has('sms_urgent')
        ];

        // Sauvegarder dans le champ permissions (ou créer un nouveau champ notifications)
        $currentPermissions = json_decode($user->permissions ?? '{}', true);
        $currentPermissions['notifications'] = $notifications;

        $user->update([
            'permissions' => json_encode($currentPermissions)
        ]);

        return redirect()->route('profile.index')
            ->with('success', 'Préférences de notification mises à jour');
    }

    /**
     * Exporter les données personnelles (RGPD)
     */
    public function exportData()
    {
        $user = Auth::user();

        $data = [
            'informations_personnelles' => [
                'prenom' => $user->prenom,
                'nom' => $user->nom,
                'email' => $user->email,
                'telephone' => $user->telephone,
                'role' => $user->role,
                'date_creation' => $user->created_at,
                'derniere_connexion' => $user->last_login
            ]
        ];

        // Si c'est un technicien, ajouter ses données de travail
        if ($user->role === 'technicien') {
            $data['activite_professionnelle'] = [
                'rdv_assignes' => \App\Models\Rdv::where('assigned_to', $user->id)->count(),
                'etapes_travaillees' => \App\Models\RdvStage::where('user_id', $user->id)->count(),
                'temps_total_travail' => \Illuminate\Support\Facades\DB::table('rdv_stages')
                    ->where('user_id', $user->id)
                    ->sum('actual_duration')
            ];
        }

        $filename = 'donnees_personnelles_' . $user->id . '_' . now()->format('Y-m-d') . '.json';

        return response()->json($data)
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    }
}
