@extends('layout')

@section('content')
<div class="pcoded-main-container">
    <div class="pcoded-content">
        <!-- [ breadcrumb ] start -->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10">KPI Annuels - {{ $year }}</h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}"><i class="feather icon-home"></i></a></li>
                            <li class="breadcrumb-item"><a href="#!">KPI</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!-- [ breadcrumb ] end -->

        <!-- [ Main Content ] start -->
        <div class="row">
            <!-- [ Sélecteur d'année ] start -->
            <div class="col-sm-12 mb-4">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0">Tableau de Bord KPI</h5>
                                <p class="text-muted mb-0">Indicateurs de performance pour l'année {{ $year }}</p>
                            </div>
                            <div class="col-md-6 text-right">
                                <form method="GET" class="d-inline-block">
                                    <div class="input-group">
                                        <select name="year" class="form-control" onchange="this.form.submit()">
                                            @for($y = now()->year; $y >= now()->year - 5; $y--)
                                                <option value="{{ $y }}" {{ $year == $y ? 'selected' : '' }}>{{ $y }}</option>
                                            @endfor
                                        </select>
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-primary" type="button" onclick="exportPdf()">
                                                <i class="fas fa-download mr-1"></i>Export PDF
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- [ Sélecteur d'année ] end -->

            <!-- [ KPI Généraux ] start -->
            <div class="col-md-6 col-xl-3">
                <div class="card bg-c-blue order-card">
                    <div class="card-body">
                        <h6 class="text-white">Total RDV</h6>
                        <h2 class="text-white">{{ number_format($generalKpis['total_rdv']) }}</h2>
                        <p class="m-b-0">
                            @if(isset($trends['total_rdv']))
                                <i class="fas fa-arrow-{{ $trends['total_rdv']['direction'] === 'up' ? 'up' : 'down' }} mr-1"></i>
                                {{ abs($trends['total_rdv']['trend']) }}% vs {{ $year - 1 }}
                            @endif
                        </p>
                        <i class="fas fa-calendar f-right f-26 text-c-blue"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-xl-3">
                <div class="card bg-c-green order-card">
                    <div class="card-body">
                        <h6 class="text-white">Taux de Réussite</h6>
                        <h2 class="text-white">{{ $generalKpis['taux_reussite'] }}%</h2>
                        <p class="m-b-0">{{ $generalKpis['rdv_termines'] }} RDV terminés</p>
                        <i class="fas fa-check-circle f-right f-26 text-c-green"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-xl-3">
                <div class="card bg-c-yellow order-card">
                    <div class="card-body">
                        <h6 class="text-white">Nouveaux Clients</h6>
                        <h2 class="text-white">{{ number_format($generalKpis['nouveaux_clients']) }}</h2>
                        <p class="m-b-0">
                            @if(isset($trends['nouveaux_clients']))
                                <i class="fas fa-arrow-{{ $trends['nouveaux_clients']['direction'] === 'up' ? 'up' : 'down' }} mr-1"></i>
                                {{ abs($trends['nouveaux_clients']['trend']) }}% vs {{ $year - 1 }}
                            @endif
                        </p>
                        <i class="fas fa-user-plus f-right f-26 text-c-yellow"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-xl-3">
                <div class="card bg-c-red order-card">
                    <div class="card-body">
                        <h6 class="text-white">Temps Moyen</h6>
                        <h2 class="text-white">{{ $generalKpis['temps_moyen_reparation'] }}min</h2>
                        <p class="m-b-0">Par réparation</p>
                        <i class="fas fa-clock f-right f-26 text-c-red"></i>
                    </div>
                </div>
            </div>
            <!-- [ KPI Généraux ] end -->

            <!-- [ KPI Financiers ] start -->
            <div class="col-md-6 col-xl-3">
                <div class="card bg-c-blue order-card">
                    <div class="card-body">
                        <h6 class="text-white">Chiffre d'Affaires</h6>
                        <h2 class="text-white">{{ number_format($financialKpis['chiffre_affaires'], 0) }}€</h2>
                        <p class="m-b-0">Réalisé</p>
                        <i class="fas fa-euro-sign f-right f-26 text-c-blue"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-xl-3">
                <div class="card bg-c-green order-card">
                    <div class="card-body">
                        <h6 class="text-white">Panier Moyen</h6>
                        <h2 class="text-white">{{ number_format($financialKpis['panier_moyen'], 0) }}€</h2>
                        <p class="m-b-0">Par intervention</p>
                        <i class="fas fa-shopping-cart f-right f-26 text-c-green"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-xl-3">
                <div class="card bg-c-yellow order-card">
                    <div class="card-body">
                        <h6 class="text-white">Coût Pièces</h6>
                        <h2 class="text-white">{{ number_format($financialKpis['cout_pieces'], 0) }}€</h2>
                        <p class="m-b-0">Total pièces</p>
                        <i class="fas fa-cogs f-right f-26 text-c-yellow"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-xl-3">
                <div class="card bg-c-red order-card">
                    <div class="card-body">
                        <h6 class="text-white">Marge Pièces</h6>
                        <h2 class="text-white">{{ number_format($financialKpis['marge_pieces'], 0) }}€</h2>
                        <p class="m-b-0">Bénéfice</p>
                        <i class="fas fa-chart-line f-right f-26 text-c-red"></i>
                    </div>
                </div>
            </div>
            <!-- [ KPI Financiers ] end -->

            <!-- [ Graphique Évolution Mensuelle ] start -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-line mr-2"></i>Évolution Mensuelle</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="monthlyChart" height="100"></canvas>
                    </div>
                </div>
            </div>
            <!-- [ Graphique Évolution Mensuelle ] end -->

            <!-- [ Répartition par Statut ] start -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-pie mr-2"></i>Répartition des RDV</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="statusChart" height="200"></canvas>
                        <div class="mt-3">
                            <div class="row text-center">
                                <div class="col-6">
                                    <h6 class="text-success">{{ $generalKpis['rdv_termines'] }}</h6>
                                    <p class="text-muted mb-0">Terminés</p>
                                </div>
                                <div class="col-6">
                                    <h6 class="text-danger">{{ $generalKpis['rdv_annules'] }}</h6>
                                    <p class="text-muted mb-0">Annulés</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- [ Répartition par Statut ] end -->

            <!-- [ Performance par Technicien ] start -->
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-users mr-2"></i>Performance par Technicien</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Technicien</th>
                                        <th>RDV Assignés</th>
                                        <th>RDV Terminés</th>
                                        <th>Taux de Réussite</th>
                                        <th>Temps Total (h)</th>
                                        <th>Performance</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($technicianKpis as $kpi)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-circle bg-primary text-white mr-2">
                                                        {{ strtoupper(substr($kpi['technicien']->prenom, 0, 1)) }}{{ strtoupper(substr($kpi['technicien']->nom, 0, 1)) }}
                                                    </div>
                                                    <div>
                                                        <strong>{{ $kpi['technicien']->prenom }} {{ $kpi['technicien']->nom }}</strong>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{ $kpi['rdv_assignes'] }}</td>
                                            <td>{{ $kpi['rdv_termines'] }}</td>
                                            <td>
                                                <span class="badge badge-{{ $kpi['taux_reussite'] >= 90 ? 'success' : ($kpi['taux_reussite'] >= 70 ? 'warning' : 'danger') }}">
                                                    {{ $kpi['taux_reussite'] }}%
                                                </span>
                                            </td>
                                            <td>{{ round($kpi['temps_total'] / 60, 1) }}h</td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar bg-{{ $kpi['taux_reussite'] >= 90 ? 'success' : ($kpi['taux_reussite'] >= 70 ? 'warning' : 'danger') }}" 
                                                         style="width: {{ $kpi['taux_reussite'] }}%">
                                                        {{ $kpi['taux_reussite'] }}%
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- [ Performance par Technicien ] end -->

            <!-- [ Détails Mensuels ] start -->
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-calendar-alt mr-2"></i>Détails Mensuels</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Mois</th>
                                        <th>RDV Total</th>
                                        <th>RDV Terminés</th>
                                        <th>Taux de Réussite</th>
                                        <th>Chiffre d'Affaires</th>
                                        <th>Évolution</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($monthlyKpis as $index => $monthly)
                                        @php
                                            $tauxReussite = $monthly['rdv_count'] > 0 ? round(($monthly['rdv_termines'] / $monthly['rdv_count']) * 100, 2) : 0;
                                            $previousMonth = $index > 0 ? $monthlyKpis[$index - 1] : null;
                                            $evolution = 0;
                                            if ($previousMonth && $previousMonth['chiffre_affaires'] > 0) {
                                                $evolution = round((($monthly['chiffre_affaires'] - $previousMonth['chiffre_affaires']) / $previousMonth['chiffre_affaires']) * 100, 2);
                                            }
                                        @endphp
                                        <tr>
                                            <td><strong>{{ $monthly['month_name'] }}</strong></td>
                                            <td>{{ $monthly['rdv_count'] }}</td>
                                            <td>{{ $monthly['rdv_termines'] }}</td>
                                            <td>
                                                <span class="badge badge-{{ $tauxReussite >= 90 ? 'success' : ($tauxReussite >= 70 ? 'warning' : 'danger') }}">
                                                    {{ $tauxReussite }}%
                                                </span>
                                            </td>
                                            <td>{{ number_format($monthly['chiffre_affaires'], 0) }}€</td>
                                            <td>
                                                @if($evolution != 0)
                                                    <span class="text-{{ $evolution > 0 ? 'success' : 'danger' }}">
                                                        <i class="fas fa-arrow-{{ $evolution > 0 ? 'up' : 'down' }}"></i>
                                                        {{ abs($evolution) }}%
                                                    </span>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- [ Détails Mensuels ] end -->
        </div>
        <!-- [ Main Content ] end -->
    </div>
</div>

@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Graphique évolution mensuelle
const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
const monthlyData = @json($monthlyKpis);

new Chart(monthlyCtx, {
    type: 'line',
    data: {
        labels: monthlyData.map(m => m.month_name),
        datasets: [{
            label: 'RDV Total',
            data: monthlyData.map(m => m.rdv_count),
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }, {
            label: 'RDV Terminés',
            data: monthlyData.map(m => m.rdv_termines),
            borderColor: 'rgb(54, 162, 235)',
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'Évolution des RDV par mois'
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Graphique répartition par statut
const statusCtx = document.getElementById('statusChart').getContext('2d');
const generalKpis = @json($generalKpis);

new Chart(statusCtx, {
    type: 'doughnut',
    data: {
        labels: ['Terminés', 'Annulés', 'Autres'],
        datasets: [{
            data: [
                generalKpis.rdv_termines,
                generalKpis.rdv_annules,
                generalKpis.total_rdv - generalKpis.rdv_termines - generalKpis.rdv_annules
            ],
            backgroundColor: [
                '#28a745',
                '#dc3545',
                '#6c757d'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

function exportPdf() {
    window.open('{{ route("kpi.export") }}?year={{ $year }}', '_blank');
}

// Style pour les avatars
const style = document.createElement('style');
style.textContent = `
    .avatar-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: bold;
    }
`;
document.head.appendChild(style);
</script>
@endpush
