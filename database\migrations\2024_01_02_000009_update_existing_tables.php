<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update rdv_stages table
        if (Schema::hasTable('rdv_stages')) {
            Schema::table('rdv_stages', function (Blueprint $table) {
                if (!Schema::hasColumn('rdv_stages', 'estimated_duration')) {
                    $table->integer('estimated_duration')->nullable()->after('notes');
                }
                if (!Schema::hasColumn('rdv_stages', 'actual_duration')) {
                    $table->integer('actual_duration')->nullable()->after('estimated_duration');
                }
                
                // Update status enum to match our model
                $table->enum('status', ['en_attente', 'en_cours', 'termine', 'suspendu', 'annule'])
                      ->default('en_attente')->change();
            });
        }

        // Update users table
        if (Schema::hasTable('users')) {
            Schema::table('users', function (Blueprint $table) {
                if (!Schema::hasColumn('users', 'prenom')) {
                    $table->string('prenom', 100)->nullable()->after('id');
                }
                if (!Schema::hasColumn('users', 'nom')) {
                    $table->string('nom', 100)->nullable()->after('prenom');
                }
                if (!Schema::hasColumn('users', 'telephone')) {
                    $table->string('telephone', 20)->nullable()->after('email');
                }
                if (!Schema::hasColumn('users', 'role')) {
                    $table->enum('role', ['admin', 'manager', 'technicien', 'receptionist'])->default('technicien')->after('password');
                }
                if (!Schema::hasColumn('users', 'active')) {
                    $table->boolean('active')->default(true)->after('role');
                }
                if (!Schema::hasColumn('users', 'permissions')) {
                    $table->json('permissions')->nullable()->after('active');
                }
                if (!Schema::hasColumn('users', 'theme_preference')) {
                    $table->enum('theme_preference', ['light', 'dark', 'system'])->default('system')->after('permissions');
                }
                if (!Schema::hasColumn('users', 'last_login')) {
                    $table->timestamp('last_login')->nullable()->after('theme_preference');
                }
                if (!Schema::hasColumn('users', 'avatar')) {
                    $table->string('avatar')->nullable()->after('last_login');
                }
            });
        }

        // Update rdv table
        if (Schema::hasTable('rdv')) {
            Schema::table('rdv', function (Blueprint $table) {
                if (!Schema::hasColumn('rdv', 'priority')) {
                    $table->integer('priority')->default(3)->after('current_stage_id');
                }
                if (!Schema::hasColumn('rdv', 'estimated_duration')) {
                    $table->integer('estimated_duration')->nullable()->after('priority');
                }
                if (!Schema::hasColumn('rdv', 'actual_duration')) {
                    $table->integer('actual_duration')->nullable()->after('estimated_duration');
                }
                if (!Schema::hasColumn('rdv', 'cost_estimate')) {
                    $table->decimal('cost_estimate', 10, 2)->nullable()->after('actual_duration');
                }
                if (!Schema::hasColumn('rdv', 'final_cost')) {
                    $table->decimal('final_cost', 10, 2)->nullable()->after('cost_estimate');
                }
                if (!Schema::hasColumn('rdv', 'notes')) {
                    $table->text('notes')->nullable()->after('final_cost');
                }
                if (!Schema::hasColumn('rdv', 'created_by')) {
                    $table->unsignedInteger('created_by')->nullable()->after('notes');
                }
                if (!Schema::hasColumn('rdv', 'assigned_to')) {
                    $table->unsignedInteger('assigned_to')->nullable()->after('created_by');
                }
            });
        }

        // Update stages table
        if (Schema::hasTable('stages')) {
            Schema::table('stages', function (Blueprint $table) {
                if (!Schema::hasColumn('stages', 'description')) {
                    $table->text('description')->nullable()->after('name');
                }
                if (!Schema::hasColumn('stages', 'order')) {
                    $table->integer('order')->default(0)->after('description');
                }
                if (!Schema::hasColumn('stages', 'color')) {
                    $table->string('color', 20)->default('primary')->after('order');
                }
                if (!Schema::hasColumn('stages', 'icon')) {
                    $table->string('icon', 50)->nullable()->after('color');
                }
                if (!Schema::hasColumn('stages', 'is_active')) {
                    $table->boolean('is_active')->default(true)->after('icon');
                }
                if (!Schema::hasColumn('stages', 'estimated_duration')) {
                    $table->integer('estimated_duration')->nullable()->after('is_active');
                }
            });
        }

        // Update stage_photos table
        if (Schema::hasTable('stage_photos')) {
            Schema::table('stage_photos', function (Blueprint $table) {
                if (!Schema::hasColumn('stage_photos', 'file_name')) {
                    $table->string('file_name')->after('file_path');
                }
                if (!Schema::hasColumn('stage_photos', 'mime_type')) {
                    $table->string('mime_type', 50)->nullable()->after('file_name');
                }
                if (!Schema::hasColumn('stage_photos', 'file_size')) {
                    $table->integer('file_size')->nullable()->after('mime_type');
                }
                if (!Schema::hasColumn('stage_photos', 'notes')) {
                    $table->text('notes')->nullable()->after('file_size');
                }
                if (!Schema::hasColumn('stage_photos', 'uploaded_by')) {
                    $table->unsignedInteger('uploaded_by')->after('notes');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove added columns if needed
        if (Schema::hasTable('rdv_stages')) {
            Schema::table('rdv_stages', function (Blueprint $table) {
                $table->dropColumn(['estimated_duration', 'actual_duration']);
            });
        }

        if (Schema::hasTable('users')) {
            Schema::table('users', function (Blueprint $table) {
                $table->dropColumn(['prenom', 'nom', 'telephone', 'role', 'active', 'permissions', 'theme_preference', 'last_login', 'avatar']);
            });
        }

        if (Schema::hasTable('rdv')) {
            Schema::table('rdv', function (Blueprint $table) {
                $table->dropColumn(['priority', 'estimated_duration', 'actual_duration', 'cost_estimate', 'final_cost', 'notes', 'created_by', 'assigned_to']);
            });
        }

        if (Schema::hasTable('stages')) {
            Schema::table('stages', function (Blueprint $table) {
                $table->dropColumn(['description', 'order', 'color', 'icon', 'is_active', 'estimated_duration']);
            });
        }

        if (Schema::hasTable('stage_photos')) {
            Schema::table('stage_photos', function (Blueprint $table) {
                $table->dropColumn(['file_name', 'mime_type', 'file_size', 'notes', 'uploaded_by']);
            });
        }
    }
};
