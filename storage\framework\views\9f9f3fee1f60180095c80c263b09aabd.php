<style>
	.avatar-circle {
    width: 50px;
    height: 50px;
    background-color: #5A9BD4; /* يمكنك تغيير اللون حسب ما يناسبك */
    color: white;
    border-radius: 50%;
    text-align: center;
    line-height: 50px;
    font-size: 20px;
    font-weight: bold;
    display: inline-block;
}

</style>
<nav class="pcoded-navbar menu-light">
	<div class="navbar-wrapper  ">
		<div class="navbar-content scroll-div ">

			<div class="">
				<div class="main-menu-header">
					<?php
					$user = Auth::user();
					$prenom = $user && $user->prenom ? strtoupper(substr($user->prenom, 0, 1)) : 'U';
					$nom = $user && $user->nom ? strtoupper(substr($user->nom, 0, 1)) : 'S';
					$initials = $prenom . $nom;
					?>
					<div class="img-radius avatar-circle">
						<?= $initials ?>
					</div>
					<div class="user-details">
						<div id="more-details">
							<?php
							$user = Auth::user();
							echo ($user && $user->prenom && $user->nom) ? $user->prenom . ' ' . $user->nom : 'Utilisateur';
							?>
							<i class="fa fa-caret-down"></i>
						</div>
					</div>
				</div>
				<div class="collapse" id="nav-user-link">
					<ul class="list-unstyled">
						<li class="list-group-item"><a href="<?php echo e(url('/profile')); ?>"><i class="feather icon-user m-r-5"></i>Mon Profil</a></li>
						<li class="list-group-item"><a href="<?php echo e(url('/logout')); ?>"><i class="feather icon-log-out m-r-5"></i>Déconnecter</a></li>
					</ul>
				</div>
			</div>

			<ul class="nav pcoded-inner-navbar ">
				<li class="nav-item pcoded-menu-caption">
					<label>PILOTAGE</label>
				</li>
				<li class="nav-item">
					<a href="<?php echo e(url('/')); ?>" class="nav-link "><span class="pcoded-micon"><i class="fas fa-gauge-high"></i></span><span class="pcoded-mtext">Activité</span></a>
				</li>
				<li class="nav-item">
					<a href="<?php echo e(url('/atelier')); ?>" class="nav-link "><span class="pcoded-micon"><i class="fas fa-screwdriver-wrench"></i></span><span class="pcoded-mtext">Atelier</span></a>
				</li>
				<li class="nav-item">
					<a href="<?php echo e(url('/kpi')); ?>" class="nav-link "><span class="pcoded-micon"><i class="feather icon-activity"></i></span><span class="pcoded-mtext">KPI annuels</span></a>
				</li>
				<li class="nav-item pcoded-menu-caption">
					<label>BUREAU</label>
				</li>
				<!-- Lien vers missions.php remplacé par atelier.php -->
				</li>
				<li class="nav-item"><a href="<?php echo e(url('/calendrier')); ?>" class="nav-link "><span class="pcoded-micon"><i class="fas fa-calendar"></i></span><span class="pcoded-mtext">Calendrier</span></a>
				</li>
				<li class="nav-item"><a href="<?php echo e(url('/')); ?>" class="nav-link "><span class="pcoded-micon"><i class="fas fa-car"></i></span><span class="pcoded-mtext">Véhicules de prêt</span></a>
				</li>
				<li class="nav-item"><a href="<?php echo e(url('/clients')); ?>" class="nav-link "><span class="pcoded-micon"><i class="fas fa-people-group"></i></span><span class="pcoded-mtext">Clients</span></a>
				</li>
				<li class="nav-item pcoded-menu-caption">
					<label>ATELIER</label>
				</li>
				<li class="nav-item">
					<a href="<?php echo e(url('/badgeuse')); ?>" class="nav-link "><span class="pcoded-micon"><i class="fa-solid fa-face-smile"></i></span><span class="pcoded-mtext">Badgeuse</span></a>
				</li>
				<li class="nav-item">
					<a href="<?php echo e(url('/missions')); ?>" class="nav-link "><span class="pcoded-micon"><i class="fa-solid fa-tv"></i></span><span class="pcoded-mtext">Missions atelier</span></a>
				</li>
				<li class="nav-item pcoded-menu-caption">
					<label>MAGASIN</label>
				</li>
				<li class="nav-item">
					<a href="<?php echo e(url('/magasin')); ?>" class="nav-link "><span class="pcoded-micon"><i class="fa-solid fa-store"></i></span><span class="pcoded-mtext">Magasin</span></a>
				</li>
				<li class="nav-item pcoded-menu-caption">
					<label>PARAMETRES</label>
				</li>
				<li class="nav-item"><a href="<?php echo e(url('/paramertres')); ?>" class="nav-link "><span class="pcoded-micon"><i class="feather icon-settings"></i></span><span class="pcoded-mtext">Paramètre</span></a>
				</li>

			</ul>

		</div>
	</div>
</nav><?php /**PATH C:\xampp\htdocs\autolaravel\resources\views/inc/nav.blade.php ENDPATH**/ ?>