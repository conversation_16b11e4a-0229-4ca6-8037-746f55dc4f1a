<?php

namespace App\Http\Controllers;

use App\Models\Rdv;
use App\Models\Client;
use App\Models\Car;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class CalendrierController extends Controller
{
    /**
     * Afficher le calendrier
     */
    public function index(Request $request)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $view = $request->input('view', 'month');
        $date = $request->input('date', now()->format('Y-m-d'));
        
        $currentDate = Carbon::parse($date);

        // Récupérer les rendez-vous selon la vue
        $rdvs = $this->getRdvsByView($view, $currentDate);

        // Statistiques pour la période
        $stats = $this->getStatsForPeriod($view, $currentDate);

        return view('calendrier.index', compact('rdvs', 'view', 'currentDate', 'stats'));
    }

    /**
     * Obtenir les RDV selon la vue
     */
    private function getRdvsByView($view, $currentDate)
    {
        $query = Rdv::with(['client', 'car', 'currentStage', 'assignedTo']);

        switch ($view) {
            case 'day':
                $query->whereDate('jour_rdv', $currentDate);
                break;
            case 'week':
                $startOfWeek = $currentDate->copy()->startOfWeek();
                $endOfWeek = $currentDate->copy()->endOfWeek();
                $query->whereBetween('jour_rdv', [$startOfWeek, $endOfWeek]);
                break;
            case 'month':
            default:
                $query->whereYear('jour_rdv', $currentDate->year)
                      ->whereMonth('jour_rdv', $currentDate->month);
                break;
        }

        return $query->orderBy('jour_rdv')
                    ->orderBy('heure_rdv')
                    ->get();
    }

    /**
     * Obtenir les statistiques pour la période
     */
    private function getStatsForPeriod($view, $currentDate)
    {
        $query = Rdv::query();

        switch ($view) {
            case 'day':
                $query->whereDate('jour_rdv', $currentDate);
                break;
            case 'week':
                $startOfWeek = $currentDate->copy()->startOfWeek();
                $endOfWeek = $currentDate->copy()->endOfWeek();
                $query->whereBetween('jour_rdv', [$startOfWeek, $endOfWeek]);
                break;
            case 'month':
            default:
                $query->whereYear('jour_rdv', $currentDate->year)
                      ->whereMonth('jour_rdv', $currentDate->month);
                break;
        }

        return [
            'total' => $query->count(),
            'en_attente' => $query->where('statut', 'en_attente')->count(),
            'confirme' => $query->where('statut', 'confirme')->count(),
            'en_cours' => $query->where('statut', 'en_cours')->count(),
            'termine' => $query->where('statut', 'termine')->count(),
            'annule' => $query->where('statut', 'annule')->count()
        ];
    }

    /**
     * API pour récupérer les événements du calendrier
     */
    public function getEvents(Request $request)
    {
        $start = Carbon::parse($request->input('start'));
        $end = Carbon::parse($request->input('end'));

        $rdvs = Rdv::with(['client', 'car', 'currentStage'])
            ->whereBetween('jour_rdv', [$start, $end])
            ->get();

        $events = $rdvs->map(function ($rdv) {
            $color = $this->getEventColor($rdv->statut);
            
            return [
                'id' => $rdv->id,
                'title' => $rdv->client->full_name . ' - ' . $rdv->car->basic_info,
                'start' => $rdv->jour_rdv->format('Y-m-d') . 'T' . $rdv->heure_rdv->format('H:i:s'),
                'backgroundColor' => $color,
                'borderColor' => $color,
                'textColor' => '#ffffff',
                'extendedProps' => [
                    'client' => $rdv->client->full_name,
                    'car' => $rdv->car->basic_info,
                    'status' => $rdv->formatted_status,
                    'stage' => $rdv->currentStage->name ?? 'Non défini',
                    'description' => $rdv->description
                ]
            ];
        });

        return response()->json($events);
    }

    /**
     * Obtenir la couleur selon le statut
     */
    private function getEventColor($status)
    {
        $colors = [
            'en_attente' => '#ffc107',
            'confirme' => '#17a2b8',
            'en_cours' => '#007bff',
            'termine' => '#28a745',
            'annule' => '#dc3545'
        ];

        return $colors[$status] ?? '#6c757d';
    }

    /**
     * Créer un nouveau rendez-vous
     */
    public function store(Request $request)
    {
        $request->validate([
            'client_id' => 'required|exists:clients,id',
            'car_id' => 'required|exists:cars,id',
            'jour_rdv' => 'required|date',
            'heure_rdv' => 'required|date_format:H:i',
            'type_rdv' => 'required|string',
            'description' => 'nullable|string',
            'priority' => 'nullable|integer|min:1|max:5'
        ]);

        // Combiner la date et l'heure
        $dateTime = Carbon::parse($request->jour_rdv . ' ' . $request->heure_rdv);

        $rdv = Rdv::create([
            'client_id' => $request->client_id,
            'car_id' => $request->car_id,
            'jour_rdv' => $request->jour_rdv,
            'heure_rdv' => $dateTime,
            'type_rdv' => $request->type_rdv,
            'description' => $request->description,
            'statut' => 'en_attente',
            'current_stage_id' => 1, // Première étape
            'priority' => $request->priority ?? 3,
            'created_by' => Auth::id()
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Rendez-vous créé avec succès',
            'rdv' => $rdv->load(['client', 'car'])
        ]);
    }

    /**
     * Mettre à jour un rendez-vous
     */
    public function update(Request $request, Rdv $rdv)
    {
        $request->validate([
            'jour_rdv' => 'required|date',
            'heure_rdv' => 'required|date_format:H:i',
            'type_rdv' => 'required|string',
            'description' => 'nullable|string',
            'statut' => 'required|in:en_attente,confirme,en_cours,termine,annule',
            'priority' => 'nullable|integer|min:1|max:5'
        ]);

        // Combiner la date et l'heure
        $dateTime = Carbon::parse($request->jour_rdv . ' ' . $request->heure_rdv);

        $rdv->update([
            'jour_rdv' => $request->jour_rdv,
            'heure_rdv' => $dateTime,
            'type_rdv' => $request->type_rdv,
            'description' => $request->description,
            'statut' => $request->statut,
            'priority' => $request->priority ?? $rdv->priority
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Rendez-vous mis à jour avec succès',
            'rdv' => $rdv->load(['client', 'car'])
        ]);
    }

    /**
     * Supprimer un rendez-vous
     */
    public function destroy(Rdv $rdv)
    {
        // Vérifier si le RDV peut être supprimé
        if ($rdv->statut === 'en_cours') {
            return response()->json([
                'success' => false,
                'message' => 'Impossible de supprimer un rendez-vous en cours'
            ], 400);
        }

        $rdv->delete();

        return response()->json([
            'success' => true,
            'message' => 'Rendez-vous supprimé avec succès'
        ]);
    }

    /**
     * Obtenir les créneaux disponibles pour une date
     */
    public function getAvailableSlots(Request $request)
    {
        $date = $request->input('date');
        $duration = $request->input('duration', 60); // Durée en minutes

        // Heures d'ouverture (à configurer selon vos besoins)
        $openingTime = Carbon::parse($date . ' 08:00');
        $closingTime = Carbon::parse($date . ' 18:00');

        // Récupérer les RDV existants pour cette date
        $existingRdvs = Rdv::whereDate('jour_rdv', $date)
            ->orderBy('heure_rdv')
            ->get(['heure_rdv', 'estimated_duration']);

        $availableSlots = [];
        $currentTime = $openingTime->copy();

        while ($currentTime->addMinutes($duration)->lte($closingTime)) {
            $slotStart = $currentTime->copy()->subMinutes($duration);
            $slotEnd = $currentTime->copy();

            // Vérifier si ce créneau est libre
            $isAvailable = true;
            foreach ($existingRdvs as $rdv) {
                $rdvStart = Carbon::parse($rdv->heure_rdv);
                $rdvEnd = $rdvStart->copy()->addMinutes($rdv->estimated_duration ?? 60);

                if ($slotStart->lt($rdvEnd) && $slotEnd->gt($rdvStart)) {
                    $isAvailable = false;
                    break;
                }
            }

            if ($isAvailable) {
                $availableSlots[] = [
                    'time' => $slotStart->format('H:i'),
                    'label' => $slotStart->format('H:i') . ' - ' . $slotEnd->format('H:i')
                ];
            }

            $currentTime->addMinutes(30); // Créneaux de 30 minutes
        }

        return response()->json($availableSlots);
    }
}
