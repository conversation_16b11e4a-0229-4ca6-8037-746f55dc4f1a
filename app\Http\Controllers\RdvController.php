<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Exception;

class RdvController extends Controller
{
    public function getRdvStats()
    {
        try {
            // تعيين القيم الأساسية
            $result = [
                'success' => true,
                'labels' => ['Demandes à traiter', 'Photos avant', 'Réparation Atelier', 'Facturation', 'Livraison'],
                'colors' => ["#95a5a6", "#00acc1", "#9b59b6", "#f39c12", "#1abc9c"],
                'series' => []
            ];

            // استعلام لحساب "Demandes à traiter" (statut = 'en_attente')
            $result['series'][0] = DB::table('rdv')->where('statut', 'en_attente')->count();

            // استعلام لحساب المركبات في مرحلة التصوير (current_stage_id = 2)
            $result['series'][1] = DB::table('rdv')->where('current_stage_id', 2)->count();

            // استعلام لحساب المركبات في مرحلة الإصلاح (current_stage_id بين 3 و 8)
            $result['series'][2] = DB::table('rdv')->whereBetween('current_stage_id', [3, 8])->count();

            // استعلام لحساب المركبات في مرحلة الفواتير (current_stage_id = 9)
            $result['series'][3] = DB::table('rdv')->where('current_stage_id', 9)->count();

            // استعلام لحساب المركبات في مرحلة التسليم (current_stage_id = 10)
            $result['series'][4] = DB::table('rdv')->where('current_stage_id', 10)->count();

            // حساب الإجمالي
            $result['total'] = array_sum($result['series']);

            // إرجاع البيانات بتنسيق JSON
            return response()->json($result);

        } catch (Exception $e) {
            // في حالة حدوث خطأ، إرجاع رسالة خطأ
            return response()->json([
                'success' => false,
                'error' => 'Erreur de base de données: ' . $e->getMessage()
            ], 500);
        }
    }
}
