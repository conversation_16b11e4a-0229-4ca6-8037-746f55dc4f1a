// JavaScript pour la gestion des missions et des étapes

// Fonction pour afficher des alertes
function showAlert(message, type = 'info') {
    // Créer l'élément d'alerte
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.setAttribute('role', 'alert');

    // Ajouter le message
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    `;

    // Ajouter l'alerte au conteneur
    const alertContainer = document.getElementById('alert-container');
    if (alertContainer) {
        alertContainer.appendChild(alertDiv);

        // Supprimer l'alerte après 5 secondes
        setTimeout(() => {
            alertDiv.classList.remove('show');
            setTimeout(() => {
                alertContainer.removeChild(alertDiv);
            }, 300);
        }, 5000);
    } else {
        console.error('Conteneur d\'alertes non trouvé');
        alert(message); // Fallback si le conteneur n'est pas trouvé
    }
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM fully loaded');

    // Initialiser les composants
    initializeComponents();

    // Charger les données des missions
    loadMissions();

    // Ajouter les écouteurs d'événements
    addEventListeners();

    // Vérifier si le modal de détails est présent
    if (document.getElementById('missionDetailsModal')) {
        console.log('Mission details modal found');
    } else {
        console.error('Mission details modal not found');
    }
});

// Initialisation des composants
function initializeComponents() {
    // Initialiser les tooltips Bootstrap
    $('[data-toggle="tooltip"]').tooltip();

    // Initialiser le sélecteur de date
    if (document.getElementById('filter-date')) {
        flatpickr('#filter-date', {
            dateFormat: 'd/m/Y',
            locale: 'fr'
        });
    }

    // Initialiser le gestionnaire de fichiers pour les photos avant réparation
    const photoUpload = document.getElementById('photo-upload');
    if (photoUpload) {
        // Nous avons simplifié l'écouteur d'événement 'change' pour éviter le téléchargement automatique
        // et la duplication des téléchargements. L'utilisateur utilisera uniquement le bouton de téléchargement manuel.
        console.log('Écouteur d\'événement change pour photo-upload simplifié pour éviter les téléchargements en double');

        photoUpload.addEventListener('change', function(e) {
            // Vérifier si l'utilisateur a sélectionné des fichiers
            if (e.target.files && e.target.files.length > 0) {
                console.log('Fichiers sélectionnés dans photo-upload:', e.target.files.length);

                // Mettre à jour le label du sélecteur de fichiers
                const fileLabel = document.querySelector('label.custom-file-label[for="photo-upload"]');
                if (fileLabel) {
                    fileLabel.textContent = e.target.files.length > 1 ?
                        `${e.target.files.length} fichiers sélectionnés` : e.target.files[0].name;
                }

                // Mettre à jour l'état du bouton de téléchargement
                if (typeof updateBeforeUploadButtonState === 'function') {
                    updateBeforeUploadButtonState();
                }
            }
        });
    }

    // Ajouter un bouton de téléchargement manuel pour les photos avant réparation
    console.log('Recherche du conteneur pour le bouton de téléchargement avant réparation');

    // Essayer de trouver le conteneur
    const photoBeforeContainer = document.querySelector('#photo-before-form .custom-file');
    console.log('Conteneur trouvé:', photoBeforeContainer);

    if (photoBeforeContainer) {
        // Créer un conteneur pour le bouton
        const uploadBtnContainer = document.createElement('div');
        uploadBtnContainer.className = 'mt-3';

        // Créer le bouton de téléchargement
        const uploadBtn = document.createElement('button');
        uploadBtn.id = 'manual-upload-photos-btn';
        uploadBtn.className = 'btn btn-primary';
        uploadBtn.innerHTML = '<i class="fas fa-upload mr-1"></i> Télécharger les photos';
        uploadBtn.type = 'button';

        // Ajouter l'écouteur d'événement pour le bouton
        uploadBtn.addEventListener('click', function() {
            // Vérifier si au moins un des champs contient des photos
            const photoInput = document.getElementById('photo-upload');
            const cameraInput = document.getElementById('camera-direct');

            let hasPhotos = false;

            if (photoInput && photoInput.files && photoInput.files.length > 0) {
                hasPhotos = true;
            }

            if (cameraInput && cameraInput.files && cameraInput.files.length > 0) {
                hasPhotos = true;
            }

            if (!hasPhotos) {
                showAlert('Veuillez sélectionner au moins une photo avant de télécharger', 'warning');
                return;
            }

            // Télécharger les photos
            savePhotos();
        });

        // Ajouter le bouton au conteneur
        uploadBtnContainer.appendChild(uploadBtn);

        // Ajouter le conteneur après le champ de fichier
        photoBeforeContainer.parentNode.insertBefore(uploadBtnContainer, photoBeforeContainer.nextSibling);

        // Fonction pour mettre à jour l'état du bouton
        window.updateBeforeUploadButtonState = function() {
            const photoInput = document.getElementById('photo-upload');
            const cameraInput = document.getElementById('camera-direct');

            let hasPhotos = false;

            if (photoInput && photoInput.files && photoInput.files.length > 0) {
                hasPhotos = true;
            }

            if (cameraInput && cameraInput.files && cameraInput.files.length > 0) {
                hasPhotos = true;
            }

            if (uploadBtn) {
                uploadBtn.disabled = !hasPhotos;
            }
        };

        // Initialiser l'état du bouton
        updateBeforeUploadButtonState();
        }

    // Initialiser le gestionnaire pour la caméra directe (avant réparation)
    const cameraDirect = document.getElementById('camera-direct');
    if (cameraDirect) {
        cameraDirect.addEventListener('change', function(e) {
            // Vérifier si l'utilisateur a pris une photo
            if (e.target.files && e.target.files.length > 0) {
                console.log('Photo prise avec la caméra avant réparation');

                // Mettre à jour le label
                const fileLabel = document.querySelector('label.custom-file-label[for="camera-direct"]');
                if (fileLabel) {
                    fileLabel.textContent = e.target.files.length > 1 ?
                        `${e.target.files.length} photos prises` : 'Photo prise';
                }

                // Transférer les fichiers vers l'input principal (photoUpload)
                const photoUpload = document.getElementById('photo-upload');
                if (photoUpload) {
                    console.log('Transfert des photos de la caméra vers photo-upload');

                    const dataTransfer = new DataTransfer();

                    // Ajouter les fichiers existants
                    if (photoUpload.files) {
                        for (let i = 0; i < photoUpload.files.length; i++) {
                            dataTransfer.items.add(photoUpload.files[i]);
                        }
                    }

                    // Ajouter les nouvelles photos
                    for (let i = 0; i < e.target.files.length; i++) {
                        dataTransfer.items.add(e.target.files[i]);
                    }

                    // Mettre à jour l'input principal
                    photoUpload.files = dataTransfer.files;

                    // Mettre à jour le label de l'input principal
                    const mainFileLabel = document.querySelector('label.custom-file-label[for="photo-upload"]');
                    if (mainFileLabel) {
                        mainFileLabel.textContent = photoUpload.files.length > 1 ?
                            `${photoUpload.files.length} fichiers sélectionnés` : photoUpload.files[0].name;
                    }

                    // Mettre à jour l'état du bouton de téléchargement
                    if (typeof updateBeforeUploadButtonState === 'function') {
                        updateBeforeUploadButtonState();
                    }

                    console.log('Photos transférées de la caméra vers l\'input principal (photo-upload)');
                } else {
                    console.error('Élément photo-upload non trouvé');
                }

                // Réinitialiser l'input de la caméra pour permettre de prendre d'autres photos
                e.target.value = '';
            }
        });
    }

    // Initialiser le gestionnaire de fichiers pour les photos après réparation
    const afterPhotosUpload = document.getElementById('after-photos');
    if (afterPhotosUpload) {
        // Nous avons supprimé l'écouteur d'événement 'change' pour éviter le téléchargement automatique
        // et la duplication des téléchargements. L'utilisateur utilisera uniquement le bouton de téléchargement manuel.
        console.log('Écouteur d\'événement change pour after-photos désactivé pour éviter les téléchargements en double');

        // Mettre à jour le label lors du changement de fichier (sans téléchargement automatique)
        afterPhotosUpload.addEventListener('change', function(e) {
            console.log('Mise à jour du label pour after-photos');

            // Mettre à jour le label du sélecteur de fichiers
            const fileLabel = document.querySelector('label.custom-file-label[for="after-photos"]');
            if (fileLabel && e.target.files) {
                if (e.target.files.length > 1) {
                    fileLabel.textContent = `${e.target.files.length} fichiers sélectionnés`;
                } else if (e.target.files.length === 1) {
                    fileLabel.textContent = e.target.files[0].name;
                } else {
                    fileLabel.textContent = 'Choisir des fichiers...';
                }
            }

            // Mettre à jour l'état du bouton de téléchargement
            if (typeof updateUploadButtonState === 'function') {
                updateUploadButtonState();
            }
        });
    }

    // Ajouter un bouton de téléchargement manuel
    console.log('Recherche du conteneur pour le bouton de téléchargement après réparation');

    // Essayer de trouver le conteneur
    const afterPhotosContainer = document.querySelector('#after-photos-form .custom-file');
    console.log('Conteneur trouvé (après réparation):', afterPhotosContainer);

    if (afterPhotosContainer) {
        // Créer un conteneur pour le bouton
        const uploadBtnContainer = document.createElement('div');
        uploadBtnContainer.className = 'mt-3';

        // Créer le bouton de téléchargement
        const uploadBtn = document.createElement('button');
        uploadBtn.id = 'manual-upload-after-photos-btn';
        uploadBtn.className = 'btn btn-primary';
        uploadBtn.innerHTML = '<i class="fas fa-upload mr-1"></i> Télécharger les photos';
        uploadBtn.type = 'button';

        // Ajouter l'écouteur d'événement pour le bouton
        uploadBtn.addEventListener('click', function() {
            // Vérifier si au moins un des champs contient des photos
            const afterPhotosInput = document.getElementById('after-photos');
            const afterCameraInput = document.getElementById('after-camera-direct');

            let hasPhotos = false;

            if (afterPhotosInput && afterPhotosInput.files && afterPhotosInput.files.length > 0) {
                hasPhotos = true;
            }

            if (afterCameraInput && afterCameraInput.files && afterCameraInput.files.length > 0) {
                hasPhotos = true;
            }

            if (!hasPhotos) {
                showAlert('Veuillez sélectionner au moins une photo avant de télécharger', 'warning');
                return;
            }

            // Télécharger les photos
            saveAfterPhotos();
        });

        // Ajouter le bouton au conteneur
        uploadBtnContainer.appendChild(uploadBtn);

        // Ajouter le conteneur après le champ de fichier
        afterPhotosContainer.parentNode.insertBefore(uploadBtnContainer, afterPhotosContainer.nextSibling);

        // Fonction pour mettre à jour l'état du bouton
        window.updateUploadButtonState = function() {
            const afterPhotosInput = document.getElementById('after-photos');
            const afterCameraInput = document.getElementById('after-camera-direct');

            let hasPhotos = false;

            if (afterPhotosInput && afterPhotosInput.files && afterPhotosInput.files.length > 0) {
                hasPhotos = true;
            }

            if (afterCameraInput && afterCameraInput.files && afterCameraInput.files.length > 0) {
                hasPhotos = true;
            }

            if (uploadBtn) {
                uploadBtn.disabled = !hasPhotos;
            }
        };

        // Initialiser l'état du bouton
        updateUploadButtonState();
        }

    // Initialiser le gestionnaire pour la caméra directe (après réparation)
    const afterCameraDirect = document.getElementById('after-camera-direct');
    if (afterCameraDirect) {
        afterCameraDirect.addEventListener('change', function(e) {
            // Vérifier si l'utilisateur a pris une photo
            if (e.target.files && e.target.files.length > 0) {
                console.log('Photo prise avec la caméra après réparation');

                // Mettre à jour le label
                const fileLabel = document.querySelector('label.custom-file-label[for="after-camera-direct"]');
                if (fileLabel) {
                    fileLabel.textContent = e.target.files.length > 1 ?
                        `${e.target.files.length} photos prises` : 'Photo prise';
                }

                // Transférer les fichiers vers l'input principal (afterPhotosUpload)
                const afterPhotosUpload = document.getElementById('after-photos');
                if (afterPhotosUpload) {
                    console.log('Transfert des photos de la caméra vers after-photos');

                    const dataTransfer = new DataTransfer();

                    // Ajouter les fichiers existants
                    if (afterPhotosUpload.files) {
                        for (let i = 0; i < afterPhotosUpload.files.length; i++) {
                            dataTransfer.items.add(afterPhotosUpload.files[i]);
                        }
                    }

                    // Ajouter les nouvelles photos
                    for (let i = 0; i < e.target.files.length; i++) {
                        dataTransfer.items.add(e.target.files[i]);
                    }

                    // Mettre à jour l'input principal
                    afterPhotosUpload.files = dataTransfer.files;

                    // Mettre à jour le label de l'input principal
                    const mainFileLabel = document.querySelector('label.custom-file-label[for="after-photos"]');
                    if (mainFileLabel) {
                        mainFileLabel.textContent = afterPhotosUpload.files.length > 1 ?
                            `${afterPhotosUpload.files.length} fichiers sélectionnés` : afterPhotosUpload.files[0].name;
                    }

                    // Mettre à jour l'état du bouton de téléchargement
                    if (typeof updateUploadButtonState === 'function') {
                        updateUploadButtonState();
                    }

                    console.log('Photos transférées de la caméra vers l\'input principal (after-photos)');
                } else {
                    console.error('Élément after-photos non trouvé');
                }

                // Réinitialiser l'input de la caméra pour permettre de prendre d'autres photos
                e.target.value = '';
            }
        });
    }
}

// Ajouter les écouteurs d'événements
function addEventListeners() {
    // Les écouteurs pour les filtres ne sont plus nécessaires car ils sont gérés par le formulaire HTML

    // Écouteur pour le formulaire d'enregistrement des photos
    if (document.getElementById('photo-before-form')) {
        document.getElementById('photo-before-form').addEventListener('submit', function(e) {
            if (!savePhotos()) {
                e.preventDefault();
            }
        });
    }

    // Écouteur pour le formulaire de complétion de l'étape
    if (document.getElementById('complete-stage-form')) {
        document.getElementById('complete-stage-form').addEventListener('submit', function(e) {
            if (!completeStage()) {
                e.preventDefault();
            }
        });
    }
}

// Cette fonction n'est plus nécessaire car les missions sont chargées côté serveur
// Nous la gardons vide pour éviter les erreurs si elle est appelée ailleurs
function loadMissions() {
    console.log('Les missions sont maintenant chargées côté serveur');
    // Rien à faire ici, les missions sont chargées par PHP
}

// Cette fonction n'est plus nécessaire car les missions sont affichées côté serveur
// Nous la gardons vide pour éviter les erreurs si elle est appelée ailleurs
function displayMissions(missions) {
    console.log('Les missions sont maintenant affichées côté serveur');
    // Rien à faire ici, les missions sont affichées par PHP
}

// Variable globale pour stocker l'ID de la mission actuelle
let currentMissionId = null;

// Ouvrir le modal de détails de la mission
function openMissionDetails(missionId) {
    console.log('Ouverture des détails de la mission:', missionId);
    currentMissionId = missionId;

    // Vérifier si le modal existe
    const modalBody = document.getElementById('missionDetailsModalBody');
    if (!modalBody) {
        console.error('Modal body not found!');
        alert('Erreur: Le modal de détails n\'a pas été trouvé.');
        return;
    }

    // Afficher un indicateur de chargement dans le modal
    modalBody.innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="sr-only">Chargement...</span>
            </div>
            <p class="mt-3">Chargement des détails de la mission...</p>
        </div>
    `;

    // Ouvrir le modal pendant le chargement
    try {
        $('#missionDetailsModal').modal('show');
    } catch (e) {
        console.error('Error showing modal:', e);
        alert('Erreur lors de l\'ouverture du modal. Vérifiez que Bootstrap est correctement chargé.');
        return;
    }

    // URL de l'API avec timestamp pour éviter le cache
    const url = `get_mission_details.php?id=${missionId}&t=${new Date().getTime()}`;
    console.log('Fetching from URL:', url);

    // Faire l'appel AJAX pour récupérer les détails
    fetch(url)
        .then(response => {
            console.log('Response status:', response.status);
            if (!response.ok) {
                throw new Error(`Erreur réseau: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Data received:', data);

            // Afficher les informations de débogage
            if (data.debug) {
                console.log('DEBUG - Nombre de pièces:', data.debug.parts_count);
                console.log('DEBUG - Échantillon de pièces:', data.debug.parts_sample);
                console.log('DEBUG - Étape actuelle:', data.debug.current_stage);
                console.log('DEBUG - ID du RDV:', data.debug.rdv_id);
            }

            // Afficher toutes les pièces détachées
            if (data.mission && data.mission.required_parts) {
                console.log('DEBUG - Toutes les pièces détachées:', data.mission.required_parts);
            }

            if (data.success) {
                populateMissionDetails(data.mission);
            } else {
                modalBody.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle mr-2"></i>${data.error}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            modalBody.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle mr-2"></i>Erreur de connexion au serveur: ${error.message}
                </div>
            `;
        });
}

// Remplir le modal avec les détails de la mission
function populateMissionDetails(mission) {
    // Reconstruire le contenu du modal
    const modalBody = document.getElementById('missionDetailsModalBody');

    // Vérifier si le modal existe
    if (!modalBody) {
        console.error('Modal body not found');
        return;
    }

    // Créer le contenu HTML pour les informations du véhicule et du client
    let html = `
        <!-- Informations du véhicule et du client -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-car mr-2"></i>Informations du véhicule</h5>
                        <div class="row">
                            <div class="col-6"><strong>Marque:</strong> <span id="car-brand">${mission.car_brand}</span></div>
                            <div class="col-6"><strong>Modèle:</strong> <span id="car-model">${mission.car_model}</span></div>
                            <div class="col-6"><strong>Immatriculation:</strong> <span id="car-plate">${mission.car_plate}</span></div>
                            <div class="col-6"><strong>N° Chassis:</strong> <span id="car-vin">${mission.car_vin}</span></div>
                            <div class="col-12"><strong>Date 1ère mise en circulation:</strong> <span id="car-first-date">${mission.car_first_date || 'Non renseignée'}</span></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-user mr-2"></i>Informations du client</h5>
                        <div class="row">
                            <div class="col-6"><strong>Nom:</strong> <span id="client-name">${mission.client_name}</span></div>
                            <div class="col-6"><strong>Prénom:</strong> <span id="client-firstname">${mission.client_firstname}</span></div>
                            <div class="col-6"><strong>Téléphone:</strong> <span id="client-phone">${mission.client_phone}</span></div>
                            <div class="col-6"><strong>Email:</strong> <span id="client-email">${mission.client_email}</span></div>
                            <div class="col-12"><strong>Adresse:</strong> <span id="client-address">${mission.client_address}</span></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Toujours afficher les étapes, qu'elles soient disponibles ou non
    // Récupérer le statut de l'étape actuelle
    const stageStatus = mission.current_stage_status || 'en_attente';

    // Changer "En attente" en "En cours" pour toutes les étapes sauf la première
    let stageStatusLabel;
    if (mission.current_stage_id == 1) {
        stageStatusLabel = stageStatus === 'confirme' ? 'Confirmé' : 'En attente';
    } else {
        stageStatusLabel = stageStatus === 'confirme' ? 'Confirmé' : 'En cours';
    }

    const stageStatusClass = stageStatus === 'confirme' ? 'success' : 'warning';

    html += `
        <!-- Étapes de la mission -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-tasks mr-2"></i>Progression des étapes
                    <span class="badge badge-${stageStatusClass} float-right">${mission.current_stage_id == 1 ? (stageStatus === 'confirme' ? 'Confirmé' : 'En attente') : (stageStatus === 'confirme' ? 'Confirmé' : 'En cours')}</span>
                </h5>
                <div class="progress-container">
                    <ul class="progressbar">
    `;

    // Ajouter un commentaire pour indiquer que les étapes sont gérées par custom-stages.js
    html += `<li class="pending">Chargement des étapes...</li>`;

    // Ajouter un attribut data-stages pour stocker les informations sur les étapes
    const stagesData = {
        current_stage_id: mission.current_stage_id,
        current_stage_order: mission.current_stage_order

    };

    // Convertir les données en JSON et les stocker dans un attribut data
    const stagesDataJson = JSON.stringify(stagesData);
    html += `<script>
        document.getElementById('missionDetailsModalBody').setAttribute('data-stages', '${stagesDataJson}');
    </script>`;


    html += `
                    </ul>
                </div>
            </div>
        </div>
    `;

    // Ajouter le contenu spécifique à l'étape actuelle
    if (mission.current_stage_id == 2) {
        // Étape de photographie avant réparation
        html += `
            <!-- Étape actuelle: Photos avant réparation -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-camera mr-2"></i>Étape 2: Photos avant réparation</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-4">
                        <i class="fas fa-info-circle mr-2"></i>
                        Cette étape consiste à prendre des photos du véhicule avant réparation pour documenter l'état initial. Prenez des photos de tous les angles et des zones endommagées.
                    </div>

                    <form id="photo-before-form" method="post" action="atelier.php" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="upload_photos">
                        <input type="hidden" name="rdv_id" value="${mission.id}">

                        <div class="form-group">
                            <label for="photo-upload">Photos avant réparation <span class="text-danger">*</span></label>
                            <div class="row">
                                <div class="col-md-12 mb-2">
                                    <div class="custom-file">
                                        <input type="file" class="custom-file-input" id="photo-upload" name="photos[]" multiple accept="image/*">
                                        <label class="custom-file-label" for="photo-upload">Choisir des fichiers</label>
                                    </div>
                                    <small class="form-text text-muted">Sélectionnez depuis la galerie</small>
                                </div>
                            </div>
                            <small class="form-text text-muted mt-2">Formats acceptés: JPG, PNG. Taille maximale: 5MB par photo.</small>
                        </div>

                        <div class="form-group">
                            <label for="photo-notes">Notes <span class="text-muted">(optionnel)</span></label>
                            <textarea class="form-control" id="photo-notes" name="notes" rows="3" placeholder="Notes sur les photos avant réparation (optionnel)">${mission.photos && mission.photos.length > 0 ? mission.photos[0].notes || '' : ''}</textarea>
                        </div>

                        <div class="text-right mt-4">
                            <button type="submit" class="btn btn-info" id="save-photos-btn" style="display: none;">
                                <i class="fas fa-save mr-2"></i>Enregistrer les photos
                            </button>
                        </div>
                    </form>

                    <!-- Affichage des photos déjà téléchargées -->
                    <div class="mt-5" id="before-photos-container">
                        <h5 class="mb-3">Photos téléchargées</h5>
                        <div class="row" id="photo-preview-container">
                            ${mission.photos && mission.photos.length > 0 ?
                                mission.photos.map(photo => `
                                    <div class="col-md-4 col-sm-6 mb-4" data-photo-id="${photo.id}">
                                        <div class="card h-100 position-relative">
                                            <button type="button" class="btn-delete-photo" onclick="removeExistingPhoto(${photo.id})" title="Supprimer cette photo">
                                                <i class="fas fa-times"></i>
                                            </button>
                                            <img src="${photo.file_path}" class="card-img-top" alt="${photo.file_name}">
                                            <div class="card-body">
                                                <p class="card-text small text-muted">${photo.file_name}</p>
                                                <p class="card-text small">${photo.upload_date ? new Date(photo.upload_date).toLocaleString() : ''}</p>
                                            </div>
                                        </div>
                                    </div>
                                `).join('') :
                                `<div class="col-12"><div class="alert alert-warning">Aucune photo avant réparation n'a été téléchargée pour ce véhicule.</div></div>`
                            }
                        </div>
                    </div>

                    <!-- Formulaire pour compléter l'étape -->
                    <div class="mt-5">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0"><i class="fas fa-check-circle mr-2"></i>Terminer l'étape</h5>
                            </div>
                            <div class="card-body">
                                <p>Une fois que vous avez téléchargé toutes les photos nécessaires, vous pouvez terminer cette étape et passer à l'évaluation des dommages.</p>
                                <form id="complete-stage-form" method="post" action="atelier.php">
                                    <input type="hidden" name="action" value="complete_stage">
                                    <input type="hidden" name="rdv_id" value="${mission.id}">
                                    <input type="hidden" name="stage_id" value="2">
                                    <input type="hidden" name="stage_status" value="confirme">

                                    <div class="text-right">
                                        <button type="submit" class="btn btn-success" id="complete-stage-btn">
                                            <i class="fas fa-check-circle mr-2"></i>Terminer et passer à l'évaluation
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    } else if (mission.current_stage_id == 3) {
        // Étape de démontage et évaluation des dommages
        html += `
            <!-- Étape actuelle: Démontage et évaluation des dommages -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-tools mr-2"></i>Étape 3: Démontage et évaluation des dommages</h5>
                </div>
                <div class="card-body">
                    <form id="damage-assessment-form" method="post" action="atelier.php">
                        <input type="hidden" name="action" value="complete_stage">
                        <input type="hidden" name="rdv_id" value="${mission.id}">
                        <input type="hidden" name="stage_id" value="3">

                        <div class="form-group">
                            <label for="damage-assessment">Évaluation des dommages</label>
                            <textarea class="form-control" id="damage-assessment" name="damage_assessment" rows="5" placeholder="Décrivez en détail les dommages constatés sur le véhicule..." required></textarea>
                            <small class="form-text text-muted">Soyez précis dans votre description pour faciliter l'estimation des réparations nécessaires.</small>
                        </div>

                        <div class="form-group mt-4">
                            <label>Liste des pièces nécessaires</label>
                            <div id="parts-container">
                                <div class="row part-row mb-2">
                                    <div class="col-md-6">
                                        <input type="text" class="form-control" name="parts[]" placeholder="Nom de la pièce" required>
                                    </div>
                                    <div class="col-md-2">
                                        <input type="number" class="form-control" name="quantities[]" placeholder="Qté" min="1" value="1">
                                    </div>
                                    <div class="col-md-3">
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">€</span>
                                            </div>
                                            <input type="text" class="form-control" name="prices[]" placeholder="Prix estimé">
                                        </div>
                                    </div>
                                    <div class="col-md-1">
                                        <button type="button" class="btn btn-danger btn-sm remove-part" disabled>
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <button type="button" class="btn btn-outline-primary btn-sm mt-2" id="add-part-btn">
                                <i class="fas fa-plus mr-1"></i> Ajouter une pièce
                            </button>
                        </div>

                        <div class="text-right mt-4">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Fermer</button>
                            <button type="submit" class="btn btn-success" id="complete-assessment-btn">
                                <i class="fas fa-check-circle mr-2"></i>Terminer l'évaluation
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;
    } else if (mission.current_stage_id == 4) {
        // Étape d'attente des pièces détachées
        html += `
            <!-- Étape actuelle: Attente des pièces -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-warning text-white">
                    <h5 class="mb-0"><i class="fas fa-shopping-cart mr-2"></i>Étape 4: Attente des pièces</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle mr-2"></i>
                        Gérez les pièces nécessaires pour cette réparation. Une fois toutes les pièces reçues, vous pourrez passer à l'étape suivante.
                    </div>

                    <!-- Formulaire d'ajout de pièce -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-plus-circle mr-2"></i>Ajouter une nouvelle pièce</h6>
                        </div>
                        <div class="card-body">
                            <form id="add-part-form" class="row">
                                <input type="hidden" id="add-part-rdv-id" value="${mission.id}">

                                <div class="col-md-4 mb-3">
                                    <label for="part-name">Nom de la pièce *</label>
                                    <input type="text" class="form-control" id="part-name" required>
                                </div>

                                <div class="col-md-2 mb-3">
                                    <label for="part-number">Référence</label>
                                    <input type="text" class="form-control" id="part-number">
                                </div>

                                <div class="col-md-2 mb-3">
                                    <label for="part-quantity">Quantité</label>
                                    <input type="number" class="form-control" id="part-quantity" min="1" value="1">
                                </div>

                                <div class="col-md-2 mb-3">
                                    <label for="part-price">Prix (€)</label>
                                    <input type="number" class="form-control" id="part-price" min="0" step="0.01">
                                </div>

                                <div class="col-md-2 mb-3">
                                    <label for="part-status">Statut</label>
                                    <select class="form-control" id="part-status">
                                        <option value="en_attente">En cours</option>
                                        <option value="commande">Commandée</option>
                                        <option value="recu">Reçue</option>
                                    </select>
                                </div>

                                <div class="col-md-12 mb-3">
                                    <label for="part-notes">Notes</label>
                                    <textarea class="form-control" id="part-notes" rows="2"></textarea>
                                </div>

                                <div class="col-md-12 text-right">
                                    <button type="button" class="btn btn-primary" id="add-part-btn">
                                        <i class="fas fa-plus mr-2"></i>Ajouter la pièce
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Liste des pièces -->
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-list mr-2"></i>Liste des pièces requises</h6>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>Nom</th>
                                            <th>Référence</th>
                                            <th>Quantité</th>
                                            <th>Prix</th>
                                            <th>Statut</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="parts-table-body">
                                        <tr>
                                            <td colspan="6" class="text-center py-3">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="sr-only">Chargement...</span>
                                                </div>
                                                <p class="mb-0 mt-2">Chargement des pièces...</p>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Boutons d'action -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <a href="magasin.php" class="btn btn-info" target="_blank">
                                <i class="fas fa-warehouse mr-2"></i>Consulter l'inventaire
                            </a>
                        </div>
                        <div class="col-md-6 text-right">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Fermer</button>
                            <button type="button" class="btn btn-success" id="complete-parts-stage-btn" disabled>
                                <i class="fas fa-check-circle mr-2"></i>Confirmer réception de toutes les pièces
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    } else if (mission.current_stage_id == 5) {
        // Étape d'installation et réparation
        html += `
            <!-- Étape actuelle: Installation et réparation -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-wrench mr-2"></i>Étape 5: Installation et réparation</h5>
                </div>
                <div class="card-body">`;

        // Vérifier si cette étape est un retour du contrôle qualité
        if (mission.return_from_control) {
            html += `
                <div class="alert alert-danger mb-4">
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-exclamation-triangle fa-2x mr-3"></i>
                        <h5 class="mb-0">Véhicule renvoyé du contrôle qualité</h5>
                    </div>
                    <p class="mb-2"><strong>Raison du retour:</strong></p>
                    <p class="mb-0">${mission.return_reason}</p>
                </div>
            `;
        }

        html += `
                <form id="repair-form" method="post" action="atelier.php">
                    <input type="hidden" name="action" value="complete_stage">
                    <input type="hidden" name="rdv_id" value="${mission.id}">
                    <input type="hidden" name="stage_id" value="5">
                    <input type="hidden" name="stage_status" value="confirme">

                    <div class="form-group">
                        <label for="repair-notes">Notes de réparation <span class="text-muted">(optionnel)</span></label>
                        <textarea class="form-control" id="repair-notes" name="repair_notes" rows="5" placeholder="Décrivez les réparations effectuées, les difficultés rencontrées, etc. (optionnel)"></textarea>
                        <small class="form-text text-muted">Ces notes seront enregistrées dans l'historique du véhicule si renseignées.</small>
                    </div>

                        <div class="form-group">
                            <label>Pièces disponibles pour la réparation</label>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle mr-2"></i>
                                Voici la liste des pièces disponibles pour cette réparation. Assurez-vous que toutes les pièces nécessaires sont disponibles avant de terminer la réparation.
                            </div>
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Pièce</th>
                                            <th>Quantité</th>
                                            <th>Statut</th>
                                        </tr>
                                    </thead>
                                    <tbody id="used-parts-table-body">
                                        <!-- Les pièces seront chargées ici -->
                                        <tr>
                                            <td colspan="3" class="text-center">
                                                <i class="fas fa-info-circle mr-2"></i>Chargement des pièces...
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="text-right mt-4">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Fermer</button>
                            <button type="submit" class="btn btn-success" id="complete-repair-btn">
                                <i class="fas fa-check-circle mr-2"></i>Terminer la réparation
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;
    } else if (mission.current_stage_id == 6) {
        // Étape de contrôle qualité
        html += `
            <!-- Étape actuelle: Contrôle qualité -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-check-circle mr-2"></i>Étape 6: Contrôle qualité</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle mr-2"></i>
                        Vérifiez la qualité des réparations effectuées. Vous pouvez soit approuver la qualité et passer à l'étape suivante, soit renvoyer le véhicule à l'étape de réparation si des problèmes sont détectés.
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-success h-100">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0"><i class="fas fa-thumbs-up mr-2"></i>Approuver la qualité</h5>
                                </div>
                                <div class="card-body">
                                    <p>Si la qualité des réparations est satisfaisante, approuvez et passez à l'étape suivante.</p>
                                    <form id="approve-quality-form" method="post" action="atelier.php">
                                        <input type="hidden" name="action" value="complete_stage">
                                        <input type="hidden" name="rdv_id" value="${mission.id}">
                                        <input type="hidden" name="stage_id" value="6">
                                        <input type="hidden" name="stage_status" value="confirme">

                                        <div class="form-group">
                                            <label for="quality-notes">Notes de contrôle <span class="text-muted">(optionnel)</span></label>
                                            <textarea class="form-control" id="quality-notes" name="quality_notes" rows="3" placeholder="Notes sur le contrôle qualité (optionnel)"></textarea>
                                        </div>

                                        <button type="submit" class="btn btn-success btn-block" id="approve-quality-btn">
                                            <i class="fas fa-check-circle mr-2"></i>Approuver et continuer
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card border-danger h-100">
                                <div class="card-header bg-danger text-white">
                                    <h5 class="mb-0"><i class="fas fa-undo mr-2"></i>Renvoyer à la réparation</h5>
                                </div>
                                <div class="card-body">
                                    <p>Si des problèmes sont détectés, renvoyez le véhicule à l'étape de réparation.</p>
                                    <form id="return-repair-form" method="post" action="atelier.php">
                                        <input type="hidden" name="action" value="return_to_repair">
                                        <input type="hidden" name="rdv_id" value="${mission.id}">
                                        <input type="hidden" name="stage_id" value="6">

                                        <div class="form-group">
                                            <label for="return-reason">Raison du retour <span class="text-danger">*</span></label>
                                            <textarea class="form-control" id="return-reason" name="return_reason" rows="3" placeholder="Décrivez les problèmes détectés et les corrections nécessaires" required></textarea>
                                            <small class="form-text text-muted">Cette information sera transmise à l'équipe de réparation.</small>
                                        </div>

                                        <button type="submit" class="btn btn-danger btn-block" id="return-repair-btn">
                                            <i class="fas fa-undo mr-2"></i>Renvoyer à la réparation
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    } else if (mission.current_stage_id == 7) {
        // Étape de nettoyage et préparation
        html += `
            <!-- Étape actuelle: Nettoyage et préparation -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-soap mr-2"></i>Étape 7: Nettoyage et préparation</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-4">
                        <i class="fas fa-info-circle mr-2"></i>
                        Cette étape consiste à nettoyer le véhicule et à le préparer pour la livraison. Assurez-vous que le véhicule est propre et prêt pour les photos après réparation.
                    </div>

                    <form id="cleaning-form" method="post" action="atelier.php">
                        <input type="hidden" name="action" value="complete_stage">
                        <input type="hidden" name="rdv_id" value="${mission.id}">
                        <input type="hidden" name="stage_id" value="7">
                        <input type="hidden" name="stage_status" value="confirme">

                        <div class="form-group">
                            <label for="cleaning-notes">Notes de nettoyage <span class="text-muted">(optionnel)</span></label>
                            <textarea class="form-control" id="cleaning-notes" name="cleaning_notes" rows="3" placeholder="Notes sur le nettoyage et la préparation du véhicule (optionnel)"></textarea>
                            <small class="form-text text-muted">Ces notes seront enregistrées dans l'historique du véhicule si renseignées.</small>
                        </div>

                        <div class="form-group">
                            <label>Tâches de nettoyage</label>
                            <div class="card">
                                <div class="card-body">
                                    <div class="custom-control custom-checkbox mb-2">
                                        <input type="checkbox" class="custom-control-input" id="cleaning-task-1">
                                        <label class="custom-control-label" for="cleaning-task-1">Lavage extérieur</label>
                                    </div>
                                    <div class="custom-control custom-checkbox mb-2">
                                        <input type="checkbox" class="custom-control-input" id="cleaning-task-2">
                                        <label class="custom-control-label" for="cleaning-task-2">Nettoyage intérieur</label>
                                    </div>
                                    <div class="custom-control custom-checkbox mb-2">
                                        <input type="checkbox" class="custom-control-input" id="cleaning-task-3">
                                        <label class="custom-control-label" for="cleaning-task-3">Polissage</label>
                                    </div>
                                    <div class="custom-control custom-checkbox mb-2">
                                        <input type="checkbox" class="custom-control-input" id="cleaning-task-4">
                                        <label class="custom-control-label" for="cleaning-task-4">Nettoyage des vitres</label>
                                    </div>
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="cleaning-task-5">
                                        <label class="custom-control-label" for="cleaning-task-5">Vérification des niveaux</label>
                                    </div>
                                </div>
                            </div>
                            <small class="form-text text-muted mt-2">Ces cases à cocher sont pour votre suivi personnel et ne sont pas enregistrées.</small>
                        </div>

                        <div class="text-right mt-4">
                            <button type="submit" class="btn btn-success" id="complete-cleaning-btn">
                                <i class="fas fa-check-circle mr-2"></i>Terminer le nettoyage et continuer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;
    } else if (mission.current_stage_id == 8) {
        // Étape de photographie après réparation
        html += `
            <!-- Étape actuelle: Photos après réparation -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-camera mr-2"></i>Étape 8: Photos après réparation</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-4">
                        <i class="fas fa-info-circle mr-2"></i>
                        Cette étape consiste à prendre des photos du véhicule après réparation pour documenter l'état final. Prenez des photos de tous les angles et des zones qui ont été réparées.
                    </div>

                    <form id="upload-after-photos-form" method="post" action="atelier.php" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="upload_after_photos">
                        <input type="hidden" name="rdv_id" value="${mission.id}">

                        <div class="form-group">
                            <label for="after-photos">Photos après réparation <span class="text-danger">*</span></label>
                            <div class="row">
                                <div class="col-md-12 mb-2">
                                    <div class="custom-file">
                                        <input type="file" class="custom-file-input" id="after-photos" name="photos[]" multiple accept="image/*">
                                        <label class="custom-file-label" for="after-photos">Choisir des fichiers...</label>
                                    </div>
                                    <small class="form-text text-muted">Sélectionnez depuis la galerie</small>
                                </div>
                            </div>
                            <small class="form-text text-muted mt-2">Formats acceptés: JPG, PNG. Taille maximale: 5MB par photo.</small>
                        </div>

                        <div class="form-group">
                            <label for="after-photos-notes">Notes <span class="text-muted">(optionnel)</span></label>
                            <textarea class="form-control" id="after-photos-notes" name="notes" rows="3" placeholder="Notes sur les photos après réparation (optionnel)"></textarea>
                        </div>

                        <div class="text-right mt-4">

                            <button type="submit" class="btn btn-primary" id="upload-after-photos-btn" style="display: none;">
                                <i class="fas fa-upload mr-2"></i>Télécharger les photos (auto)
                            </button>
                        </div>
                    </form>

                    <!-- Affichage des photos déjà téléchargées -->
                    <div class="mt-5" id="after-photos-container">
                        <h5 class="mb-3">Photos téléchargées</h5>
                        <div class="row" id="after-photos-gallery">
                            ${mission.after_photos && mission.after_photos.length > 0 ?
                                mission.after_photos.map(photo => `
                                    <div class="col-md-4 col-sm-6 mb-4" data-photo-id="${photo.id}">
                                        <div class="card h-100 position-relative">
                                            <button type="button" class="btn-delete-photo" onclick="removeExistingPhoto(${photo.id})" title="Supprimer cette photo">
                                                <i class="fas fa-times"></i>
                                            </button>
                                            <img src="${photo.file_path}" class="card-img-top" alt="${photo.file_name}">
                                            <div class="card-body">
                                                <p class="card-text small text-muted">${photo.file_name}</p>
                                                <p class="card-text small">${photo.upload_date ? new Date(photo.upload_date).toLocaleString() : ''}</p>
                                            </div>
                                        </div>
                                    </div>
                                `).join('') :
                                `<div class="col-12"><div class="alert alert-warning">Aucune photo après réparation n'a été téléchargée pour ce véhicule.</div></div>`
                            }
                        </div>
                    </div>

                    <!-- Formulaire pour compléter l'étape -->
                    <div class="mt-5">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0"><i class="fas fa-check-circle mr-2"></i>Terminer l'étape</h5>
                            </div>
                            <div class="card-body">
                                <p>Une fois que vous avez téléchargé toutes les photos nécessaires, vous pouvez terminer cette étape et passer à la facturation.</p>
                                <form id="complete-after-photos-form" method="post" action="atelier.php">
                                    <input type="hidden" name="action" value="complete_stage">
                                    <input type="hidden" name="rdv_id" value="${mission.id}">
                                    <input type="hidden" name="stage_id" value="8">
                                    <input type="hidden" name="stage_status" value="confirme">

                                    <div class="text-right">
                                        <button type="submit" class="btn btn-success" id="complete-after-photos-btn">
                                            <i class="fas fa-check-circle mr-2"></i>Terminer et passer à la facturation
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    } else if (mission.current_stage_id == 9) {
        // Étape de facturation
        // Calculer le total des pièces
        let totalParts = 0;
        let parts = [];

        // Vérifier si mission.required_parts existe
        if (mission.required_parts && mission.required_parts.length > 0) {
            console.log('Pièces disponibles pour la facturation:', mission.required_parts);

            // Inclure toutes les pièces, pas seulement celles qui sont marquées comme reçues
            parts = mission.required_parts;

            // Afficher chaque pièce dans la console pour le débogage
            parts.forEach((part, index) => {
                console.log(`Pièce #${index + 1}:`, part);
                console.log(`  - Nom: ${part.part_name}`);
                console.log(`  - Référence: ${part.part_number || 'N/A'}`);
                console.log(`  - Quantité: ${part.quantity}`);
                console.log(`  - Prix: ${part.price || 'N/A'}`);
                console.log(`  - Statut: ${part.status || 'N/A'}`);

                // Calculer le total
                totalParts += part.quantity * (part.price || 0);
            });
        } else {
            console.log('Aucune pièce trouvée pour la facturation');
            console.log('Tentative de récupération des pièces via l\'API...');

           

            console.log('Pièces de test ajoutées:', parts);

            // Calculer le total des pièces de test
            parts.forEach(part => {
                totalParts += part.quantity * (part.price || 0);
            });
        }

        // Calculer la TVA (20%)
        const tva = totalParts * 0.20;

        // Calculer le total TTC
        const totalTTC = totalParts + tva;

        // Générer un numéro de facture unique
        const invoiceNumber = mission.existing_invoice ? mission.existing_invoice.invoice_number : 'RAP-' + new Date().toISOString().slice(0, 10).replace(/-/g, '') + '-' + mission.id;

        html += `
            <!-- Étape actuelle: Facturation -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-warning text-white">
                    <h5 class="mb-0"><i class="fas fa-file-invoice-dollar mr-2"></i>Étape 9: Facturation</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-4">
                        <i class="fas fa-info-circle mr-2"></i>
                        Cette étape consiste à générer la facture pour les réparations effectuées. Vérifiez les informations ci-dessous et cliquez sur "Générer la facture" pour créer le PDF.
                    </div>

                    <!-- Rapport de réparation -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0"><i class="fas fa-clipboard-list mr-2"></i>Rapport de réparation</h5>
                        </div>
                        <div class="card-body">
                            <!-- Évaluation des dommages -->
                            <h6 class="font-weight-bold">Évaluation des dommages</h6>
                            <p>${mission.assessment ? mission.assessment.assessment : 'Aucune évaluation disponible'}</p>

                            <!-- Notes des étapes -->
                            <h6 class="font-weight-bold mt-4">Notes des étapes de réparation</h6>
                            ${mission.stage_notes && mission.stage_notes.length > 0 ?
                                `<div class="accordion" id="accordionNotes">
                                    ${mission.stage_notes.map((note, index) => `
                                        <div class="card mb-0 border">
                                            <div class="card-header" id="heading${index}">
                                                <h5 class="mb-0">
                                                    <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapse${index}" aria-expanded="${index === 0 ? 'true' : 'false'}" aria-controls="collapse${index}">
                                                        ${note.stage_name}
                                                    </button>
                                                </h5>
                                            </div>
                                            <div id="collapse${index}" class="collapse ${index === 0 ? 'show' : ''}" aria-labelledby="heading${index}" data-parent="#accordionNotes">
                                                <div class="card-body">
                                                    <p>${note.notes}</p>
                                                </div>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>` :
                                `<p>Aucune note disponible</p>`
                            }

                            <!-- Photos avant réparation -->
                            <h6 class="font-weight-bold mt-4">Photos avant réparation</h6>
                            <div class="row">
                                ${mission.photos && mission.photos.length > 0 ?
                                    mission.photos.map(photo => `
                                        <div class="col-md-3 mb-3">
                                            <div class="card">
                                                <img src="${photo.file_path}" class="card-img-top" alt="Photo avant réparation">
                                                <div class="card-body p-2">
                                                    <p class="card-text small">${photo.file_name}</p>
                                                </div>
                                            </div>
                                        </div>
                                    `).join('') :
                                    `<div class="col-12"><p>Aucune photo avant réparation disponible</p></div>`
                                }
                            </div>

                            <!-- Photos après réparation -->
                            <h6 class="font-weight-bold mt-4">Photos après réparation</h6>
                            <div class="row">
                                ${mission.after_photos && mission.after_photos.length > 0 ?
                                    mission.after_photos.map(photo => `
                                        <div class="col-md-3 mb-3">
                                            <div class="card">
                                                <img src="${photo.file_path}" class="card-img-top" alt="Photo après réparation">
                                                <div class="card-body p-2">
                                                    <p class="card-text small">${photo.file_name}</p>
                                                </div>
                                            </div>
                                        </div>
                                    `).join('') :
                                    `<div class="col-12"><p>Aucune photo après réparation disponible</p></div>`
                                }
                            </div>
                        </div>
                    </div>

                    <!-- Tableau des pièces remplacées -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0"><i class="fas fa-tools mr-2"></i>PIÈCES REMPLACÉES</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Nom de la pièce</th>
                                            <th>Référence</th>
                                            <th>Quantité</th>
                                            <th>Prix unitaire</th>
                                            <th>Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${parts.length > 0 ?
                                            parts.map(part => `
                                                <tr>
                                                    <td>${part.part_name}</td>
                                                    <td>${part.part_number || 'N/A'}</td>
                                                    <td>${part.quantity}</td>
                                                    <td>${part.price ? part.price.toFixed(3) + ' DT' : 'N/A'}</td>
                                                    <td>${part.price ? (part.quantity * part.price).toFixed(3) + ' DT' : 'N/A'}</td>
                                                </tr>
                                            `).join('') :
                                            `<tr><td colspan="5" class="text-center">Aucune pièce remplacée</td></tr>`
                                        }
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <th colspan="4" class="text-right">Total HT</th>
                                            <th>${totalParts.toFixed(3)} DT</th>
                                        </tr>
                                        <tr>
                                            <th colspan="4" class="text-right">TVA (19%)</th>
                                            <th>${tva.toFixed(3)} DT</th>
                                        </tr>
                                        <tr>
                                            <th colspan="4" class="text-right">Total TTC</th>
                                            <th>${totalTTC.toFixed(3)} DT</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Formulaire de génération de rapport -->
                    <div class="card border-success">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="fas fa-file-pdf mr-2"></i>Générer le rapport</h5>
                        </div>
                        <div class="card-body">
                            <form id="invoice-form">
                                <input type="hidden" id="invoice-rdv-id" value="${mission.id}">
                                <input type="hidden" id="invoice-number" value="${invoiceNumber}">

                                <div class="form-group">
                                    <label for="invoice-notes">Notes pour le rapport</label>
                                    <textarea class="form-control" id="invoice-notes" rows="3" placeholder="Ajouter des notes supplémentaires pour le rapport...">${mission.existing_invoice ? mission.existing_invoice.notes || '' : ''}</textarea>
                                </div>

                                <div class="form-group">
                                    <label for="invoice-email">Envoyer le rapport par email à</label>
                                    <input type="email" class="form-control" id="invoice-email" placeholder="Adresse email du destinataire">
                                </div>

                                <div class="form-group">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="complete-invoice-stage" checked>
                                        <label class="custom-control-label" for="complete-invoice-stage">Terminer l'étape de facturation</label>
                                    </div>
                                </div>

                                <div class="text-right">
                                    <button type="button" class="btn btn-primary" id="generate-invoice-btn">
                                        <i class="fas fa-file-pdf mr-2"></i>Générer le rapport
                                    </button>
                                    ${mission.existing_invoice ?
                                        `<a href="invoices/${mission.existing_invoice.invoice_number}.pdf" target="_blank" class="btn btn-info ml-2">
                                            <i class="fas fa-eye mr-2"></i>Voir le rapport existant
                                        </a>` :
                                        ''
                                    }
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        `;
    } else {
        // Message pour les autres étapes
        html += `
            <div class="alert alert-info">
                <i class="fas fa-info-circle mr-2"></i>
                Cette mission est actuellement à l'étape "${mission.current_stage_name}".
            </div>
        `;
    }

    // Mettre à jour le contenu du modal
    modalBody.innerHTML = html;

    // Ajouter l'attribut data-current-stage-id au modal après avoir mis à jour le contenu
    modalBody.setAttribute('data-current-stage-id', mission.current_stage_id);

    // Ajouter un délai court pour permettre au DOM de se mettre à jour
    setTimeout(() => {
        // Ajouter l'écouteur d'événement pour le bouton de téléchargement manuel des photos avant réparation
        const manualUploadPhotosBtn = document.getElementById('manual-upload-photos-btn');
        if (manualUploadPhotosBtn) {
            console.log('Bouton de téléchargement manuel des photos avant réparation trouvé');
            manualUploadPhotosBtn.addEventListener('click', function() {
                console.log('Clic sur le bouton de téléchargement manuel des photos avant réparation');
                savePhotos();
            });
        }

        // Ajouter l'écouteur d'événement pour le bouton de téléchargement manuel des photos après réparation
        const manualUploadAfterPhotosBtn = document.getElementById('manual-upload-after-photos-btn');
        if (manualUploadAfterPhotosBtn) {
            console.log('Bouton de téléchargement manuel des photos après réparation trouvé');
            manualUploadAfterPhotosBtn.addEventListener('click', function() {
                console.log('Clic sur le bouton de téléchargement manuel des photos après réparation');
                saveAfterPhotos();
            });
        }

        // Ajouter l'écouteur d'événement pour le bouton de suppression de toutes les photos avant réparation (si présent)
        const clearPhotosBtn = document.getElementById('clear-photos-btn');
        if (clearPhotosBtn) {
            clearPhotosBtn.addEventListener('click', function() {
                if (confirm('Êtes-vous sûr de vouloir supprimer toutes les photos ?')) {
                    const photoContainer = document.getElementById('photo-preview-container');
                    if (photoContainer) {
                        photoContainer.innerHTML = '';
                    }

                    // Réinitialiser l'input file
                    const photoInput = document.getElementById('photo-upload');
                    if (photoInput) {
                        photoInput.value = '';

                        // Mettre à jour le label
                        const fileLabel = document.querySelector('.custom-file-label');
                        if (fileLabel) {
                            fileLabel.textContent = 'Choisir des fichiers';
                        }
                    }
                }
            });
        }

        // Ajouter l'écouteur d'événement pour le bouton de suppression de toutes les photos après réparation (si présent)
        const clearAfterPhotosBtn = document.getElementById('clear-after-photos-btn');
        if (clearAfterPhotosBtn) {
            clearAfterPhotosBtn.addEventListener('click', function() {
                if (confirm('Êtes-vous sûr de vouloir supprimer toutes les photos ?')) {
                    const photoInput = document.getElementById('after-photos');
                    if (photoInput) {
                        photoInput.value = '';

                        // Mettre à jour le label
                        const fileLabel = document.querySelector('.custom-file-label');
                        if (fileLabel) {
                            fileLabel.textContent = 'Choisir des fichiers...';
                        }
                    }
                }
            });
        }
    }, 100);

    // Réinitialiser les écouteurs d'événements
    if (mission.current_stage_id == 2) {
        // Configurer les boutons de téléchargement des photos (cachés)
        const savePhotosBtn = document.getElementById('save-photos-btn');
        const uploadPhotosBtn = document.getElementById('upload-photos-btn');

        // Ces boutons sont cachés mais nous les gardons fonctionnels au cas où
        if (savePhotosBtn) {
            savePhotosBtn.addEventListener('click', function(e) {
                e.preventDefault();
                savePhotos();
            });
        }

        if (uploadPhotosBtn) {
            uploadPhotosBtn.addEventListener('click', function(e) {
                e.preventDefault();
                savePhotos();
            });
        }

        // Ajouter l'écouteur d'événement pour le champ de téléchargement de photos avant réparation
        const photoUploadInput = document.getElementById('photo-upload');
        if (photoUploadInput) {
            photoUploadInput.addEventListener('change', function(event) {
                // Télécharger automatiquement les photos après sélection
                if (event.target.files && event.target.files.length > 0) {
                    handleFileSelect(event);
                    savePhotos();
                }
            });
        }

        // Ajouter l'écouteur pour le bouton de prise de photo
        const takePhotoBtn = document.getElementById('take-photo-btn');
        if (takePhotoBtn) {
            takePhotoBtn.addEventListener('click', initCamera);
        }

        // Ajouter les écouteurs pour les boutons de la caméra
        const capturePhotoBtn = document.getElementById('capture-photo-btn');
        if (capturePhotoBtn) {
            capturePhotoBtn.addEventListener('click', capturePhoto);
        }

        const cancelCameraBtn = document.getElementById('cancel-camera-btn');
        if (cancelCameraBtn) {
            cancelCameraBtn.addEventListener('click', stopCamera);
        }

        // Ajouter l'écouteur d'événement pour le bouton de complétion de l'étape
        const completeStageBtn = document.getElementById('complete-stage-btn');
        if (completeStageBtn) {
            completeStageBtn.addEventListener('click', function(e) {
                // Vérifier si des photos ont été téléchargées
                const photosContainer = document.getElementById('photo-preview-container');
                const hasPhotos = photosContainer && !photosContainer.textContent.includes('Aucune photo avant réparation');

                if (!hasPhotos) {
                    if (!confirm('Aucune photo avant réparation n\'a été téléchargée. Êtes-vous sûr de vouloir terminer cette étape ?')) {
                        e.preventDefault();
                        return;
                    }
                }

                if (!confirm('Êtes-vous sûr de vouloir terminer cette étape et passer à l\'évaluation des dommages ?')) {
                    e.preventDefault();
                }
            });
        }
    } else if (mission.current_stage_id == 3) {
        // Ajouter les écouteurs pour l'étape d'évaluation des dommages
        document.getElementById('add-part-btn').addEventListener('click', addPartRow);

        // Ajouter les écouteurs pour les boutons de suppression de pièces
        const removeButtons = document.querySelectorAll('.remove-part');
        removeButtons.forEach(button => {
            button.addEventListener('click', removePartRow);
        });
    } else if (mission.current_stage_id == 4) {
        // Vérifier si les pièces sont déjà chargées depuis get_mission_details.php
        if (mission.required_parts && mission.required_parts.length > 0) {
            console.log('Pièces déjà chargées depuis get_mission_details.php:', mission.required_parts);
            displayRequiredParts(mission.required_parts, mission.id);
        } else {
            console.log('Chargement des pièces via API get_required_parts.php');
            loadRequiredParts(mission.id);
        }

        // Ajouter les écouteurs d'événements pour la gestion des pièces
        const addPartBtn = document.getElementById('add-part-btn');
        if (addPartBtn) {
            addPartBtn.addEventListener('click', addRequiredPart);
        } else {
            console.error('Élément add-part-btn non trouvé dans le DOM');
        }

        const completePartsBtn = document.getElementById('complete-parts-stage-btn');
        if (completePartsBtn) {
            completePartsBtn.addEventListener('click', function() {
                completePartsStage(mission.id);
            });
        } else {
            console.error('Élément complete-parts-stage-btn non trouvé dans le DOM');
        }
    } else if (mission.current_stage_id == 5) {
        // Charger les pièces utilisées pour la réparation
        if (mission.required_parts && mission.required_parts.length > 0) {
            console.log('Pièces trouvées dans mission.required_parts:', mission.required_parts);
            displayUsedParts(mission.required_parts);
        } else {
            console.log('Aucune pièce trouvée dans mission.required_parts, chargement via API...');
            // Essayer de charger les pièces via l'API get_required_parts.php
            loadRequiredPartsForRepair(mission.id);
        }

        // Ajouter l'écouteur d'événement pour le bouton de complétion de la réparation
        const completeRepairBtn = document.getElementById('complete-repair-btn');
        if (completeRepairBtn) {
            completeRepairBtn.addEventListener('click', function(e) {
                if (!confirmRepairCompletion()) {
                    e.preventDefault();
                }
            });
        } else {
            console.error('Élément complete-repair-btn non trouvé dans le DOM');
        }
    } else if (mission.current_stage_id == 6) {
        // Ajouter les écouteurs d'événements pour les boutons de contrôle qualité

        // Bouton d'approbation de la qualité
        const approveQualityBtn = document.getElementById('approve-quality-btn');
        if (approveQualityBtn) {
            approveQualityBtn.addEventListener('click', function(e) {
                if (!confirm('Êtes-vous sûr de vouloir approuver la qualité des réparations et passer à l\'étape suivante ?')) {
                    e.preventDefault();
                }
            });
        } else {
            console.error('Élément approve-quality-btn non trouvé dans le DOM');
        }

        // Bouton de retour à la réparation
        const returnRepairBtn = document.getElementById('return-repair-btn');
        if (returnRepairBtn) {
            returnRepairBtn.addEventListener('click', function(e) {
                // Vérifier si une raison a été fournie
                const returnReason = document.getElementById('return-reason');
                if (!returnReason || returnReason.value.trim() === '') {
                    alert('Veuillez indiquer la raison du retour à l\'étape de réparation.');
                    e.preventDefault();
                    return;
                }

                if (!confirm('Êtes-vous sûr de vouloir renvoyer ce véhicule à l\'étape de réparation ?')) {
                    e.preventDefault();
                }
            });
        } else {
            console.error('Élément return-repair-btn non trouvé dans le DOM');
        }
    } else if (mission.current_stage_id == 7) {
        // Ajouter l'écouteur d'événement pour le bouton de complétion du nettoyage
        const completeCleaningBtn = document.getElementById('complete-cleaning-btn');
        if (completeCleaningBtn) {
            completeCleaningBtn.addEventListener('click', function(e) {
                // Vérifier si au moins une tâche de nettoyage a été cochée
                const cleaningTasks = [
                    document.getElementById('cleaning-task-1'),
                    document.getElementById('cleaning-task-2'),
                    document.getElementById('cleaning-task-3'),
                    document.getElementById('cleaning-task-4'),
                    document.getElementById('cleaning-task-5')
                ];

                const atLeastOneTaskChecked = cleaningTasks.some(task => task && task.checked);

                if (!atLeastOneTaskChecked) {
                    if (!confirm('Aucune tâche de nettoyage n\'a été cochée. Êtes-vous sûr de vouloir terminer cette étape ?')) {
                        e.preventDefault();
                        return;
                    }
                }

                if (!confirm('Êtes-vous sûr de vouloir confirmer que le nettoyage est terminé et passer à l\'étape suivante ?')) {
                    e.preventDefault();
                }
            });
        } else {
            console.error('Élément complete-cleaning-btn non trouvé dans le DOM');
        }
    } else if (mission.current_stage_id == 8) {
        // Ajouter l'écouteur d'événement pour le bouton de téléchargement des photos
        const uploadAfterPhotosBtn = document.getElementById('upload-after-photos-btn');
        if (uploadAfterPhotosBtn) {
            // Cacher le bouton de téléchargement des photos car le téléchargement est automatique
            uploadAfterPhotosBtn.style.display = 'none';

            // Ajouter l'écouteur d'événement pour le bouton de téléchargement manuel
            const manualUploadAfterPhotosBtn = document.getElementById('manual-upload-after-photos-btn');
            if (manualUploadAfterPhotosBtn) {
                manualUploadAfterPhotosBtn.addEventListener('click', function() {
                    // Vérifier si le champ contient des photos
                    const photoInput = document.getElementById('after-photos');

                    let hasPhotos = false;

                    if (photoInput && photoInput.files && photoInput.files.length > 0) {
                        hasPhotos = true;
                    }

                    if (!hasPhotos) {
                        showAlert('Veuillez sélectionner au moins une photo avant de télécharger', 'warning');
                        return;
                    }

                    // Télécharger les photos
                    saveAfterPhotos();
                });
            }

            // Ajouter l'écouteur d'événement pour le champ de téléchargement de photos après réparation
            const afterPhotosUpload = document.getElementById('after-photos');
            if (afterPhotosUpload) {
                afterPhotosUpload.addEventListener('change', function(event) {
                    // Télécharger automatiquement les photos après sélection
                    if (event.target.files && event.target.files.length > 0) {
                        saveAfterPhotos();
                    }
                });
            }

            // Ajouter l'écouteur pour le bouton de prise de photo
            const takeAfterPhotoBtn = document.getElementById('take-after-photo-btn');
            if (takeAfterPhotoBtn) {
                takeAfterPhotoBtn.addEventListener('click', initAfterCamera);
            }

            // Ajouter les écouteurs pour les boutons de la caméra
            const captureAfterPhotoBtn = document.getElementById('capture-after-photo-btn');
            if (captureAfterPhotoBtn) {
                captureAfterPhotoBtn.addEventListener('click', captureAfterPhoto);
            }

            const cancelAfterCameraBtn = document.getElementById('cancel-after-camera-btn');
            if (cancelAfterCameraBtn) {
                cancelAfterCameraBtn.addEventListener('click', stopAfterCamera);
            }
        } else {
            console.error('Élément upload-after-photos-btn non trouvé dans le DOM');
        }

        // Ajouter l'écouteur d'événement pour le bouton de complétion de l'étape
        const completeAfterPhotosBtn = document.getElementById('complete-after-photos-btn');
        if (completeAfterPhotosBtn) {
            completeAfterPhotosBtn.addEventListener('click', function(e) {
                // Vérifier si des photos ont été téléchargées
                const photosGallery = document.getElementById('after-photos-gallery');
                const hasPhotos = photosGallery && !photosGallery.textContent.includes('Aucune photo après réparation');

                if (!hasPhotos) {
                    if (!confirm('Aucune photo après réparation n\'a été téléchargée. Êtes-vous sûr de vouloir terminer cette étape ?')) {
                        e.preventDefault();
                        return;
                    }
                }

                if (!confirm('Êtes-vous sûr de vouloir terminer cette étape et passer à la facturation ?')) {
                    e.preventDefault();
                }
            });
        } else {
            console.error('Élément complete-after-photos-btn non trouvé dans le DOM');
        }
    } else if (mission.current_stage_id == 9) {
        // Ajouter l'écouteur d'événement pour le bouton de génération de facture
        const generateInvoiceBtn = document.getElementById('generate-invoice-btn');
        if (generateInvoiceBtn) {
            generateInvoiceBtn.addEventListener('click', function() {
                generateInvoice();
            });
        } else {
            console.error('Élément generate-invoice-btn non trouvé dans le DOM');
        }
    }
}

// Mettre à jour la barre de progression
function updateProgressBar(currentStageId) {
    const progressItems = document.querySelectorAll('.progressbar li');

    progressItems.forEach((item, index) => {
        item.classList.remove('active', 'current');

        if (index + 1 < currentStageId) {
            item.classList.add('active');
        } else if (index + 1 === currentStageId) {
            item.classList.add('active', 'current');
        }
    });
}

// Cette fonction n'est plus utilisée car l'affichage des étapes est géré directement dans populateMissionDetails
// function showCurrentStage(stageId) {
//     // Ici, vous afficheriez le formulaire correspondant à l'étape actuelle
//     // Pour la démonstration, nous montrons toujours l'étape de photographie
// }

// Gérer la sélection de fichiers
function handleFileSelect(event) {
    const files = event.target.files;
    const previewContainer = document.getElementById('photo-preview-container');

    if (!previewContainer) return;

    // Créer un objet FileList modifiable
    const dataTransfer = new DataTransfer();

    // Ajouter les fichiers existants au dataTransfer
    if (event.target.files) {
        for (let i = 0; i < event.target.files.length; i++) {
            dataTransfer.items.add(event.target.files[i]);
        }
    }

    // Vider le conteneur si nécessaire
    // previewContainer.innerHTML = '';

    for (let i = 0; i < files.length; i++) {
        const file = files[i];

        // Vérifier si c'est une image
        if (!file.type.match('image.*')) {
            continue;
        }

        const reader = new FileReader();

        reader.onload = (function(theFile, fileIndex) {
            return function(e) {
                // Créer l'élément de prévisualisation
                const previewItem = document.createElement('div');
                previewItem.className = 'photo-preview-item';
                previewItem.dataset.fileIndex = fileIndex; // Stocker l'index du fichier
                previewItem.innerHTML = `
                    <div class="card h-100 position-relative">
                        <button type="button" class="btn-delete-photo" title="Supprimer cette photo">
                            <i class="fas fa-times"></i>
                        </button>
                        <img src="${e.target.result}" class="card-img-top" alt="${theFile.name}" />
                        <div class="card-body">
                            <p class="card-text small text-muted">${theFile.name}</p>
                        </div>
                    </div>
                `;

                // Ajouter un écouteur d'événement pour le bouton de suppression
                const removeBtn = previewItem.querySelector('.btn-delete-photo');
                if (removeBtn) {
                    removeBtn.addEventListener('click', function() {
                        // Supprimer l'élément de prévisualisation
                        previewItem.remove();

                        // Supprimer le fichier de l'input file
                        const fileInput = event.target;
                        const newDataTransfer = new DataTransfer();

                        // Recréer la liste de fichiers sans le fichier supprimé
                        for (let j = 0; j < fileInput.files.length; j++) {
                            if (j !== parseInt(previewItem.dataset.fileIndex)) {
                                newDataTransfer.items.add(fileInput.files[j]);
                            }
                        }

                        // Mettre à jour l'input file
                        fileInput.files = newDataTransfer.files;

                        // Mettre à jour le label
                        const fileLabel = document.querySelector('.custom-file-label');
                        if (fileLabel) {
                            if (fileInput.files.length > 1) {
                                fileLabel.textContent = `${fileInput.files.length} fichiers sélectionnés`;
                            } else if (fileInput.files.length === 1) {
                                fileLabel.textContent = fileInput.files[0].name;
                            } else {
                                fileLabel.textContent = 'Choisir des fichiers';
                            }
                        }
                    });
                }

                previewContainer.appendChild(previewItem);
            };
        })(file, i);

        reader.readAsDataURL(file);
    }

    // Réinitialiser le label du sélecteur de fichier
    const fileLabel = document.querySelector('.custom-file-label');
    if (fileLabel) {
        fileLabel.textContent = files.length > 1 ? `${files.length} fichiers sélectionnés` : files[0].name;
    }
}

// Cette fonction n'est plus utilisée car la suppression est gérée directement dans handleFileSelect
// Nous la gardons pour la compatibilité avec le code existant
function removePhoto(element) {
    console.log('Cette fonction est obsolète. La suppression est maintenant gérée directement dans handleFileSelect');
    const previewItem = element.parentNode;
    if (previewItem) {
        previewItem.remove();
    }
}

// Supprimer une photo existante
function removeExistingPhoto(photoId) {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cette photo ?')) {
        return;
    }

    // Implémenter la suppression de la photo
    console.log('Suppression de la photo:', photoId);

    // Afficher un indicateur de chargement
    showAlert('Suppression de la photo en cours...', 'info');

    // Appel AJAX pour supprimer la photo
    fetch('atelier.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=delete_photo&photo_id=${photoId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('Photo supprimée avec succès', 'success');

            // Supprimer l'élément du DOM
            const photoElement = document.querySelector(`[data-photo-id="${photoId}"]`);
            if (photoElement) {
                // Ajouter une animation de disparition
                photoElement.style.transition = 'all 0.3s ease';
                photoElement.style.opacity = '0';
                photoElement.style.transform = 'scale(0.8)';

                // Supprimer l'élément après l'animation
                setTimeout(() => {
                    photoElement.remove();

                    // Vérifier si c'était la dernière photo
                    const photoContainer = photoElement.closest('.row');
                    if (photoContainer && photoContainer.children.length <= 1) {
                        // Ajouter un message "Aucune photo" si c'était la dernière
                        if (photoContainer.id === 'after-photos-gallery') {
                            photoContainer.innerHTML = '<div class="col-12 text-center text-muted"><i class="fas fa-info-circle mr-2"></i>Aucune photo après réparation</div>';
                        } else if (photoContainer.id === 'photo-preview-container') {
                            photoContainer.innerHTML = '<div class="col-12 text-center text-muted"><i class="fas fa-info-circle mr-2"></i>Aucune photo avant réparation</div>';
                        }
                    }
                }, 300);
            } else {
                console.error('Élément photo non trouvé dans le DOM');
            }
        } else {
            showAlert('Erreur lors de la suppression de la photo: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Erreur lors de la suppression de la photo:', error);
        showAlert('Erreur lors de la suppression de la photo', 'danger');
    });
}

// Ajouter une ligne de pièce détachée
function addPartRow() {
    const partsContainer = document.getElementById('parts-container');
    const partRows = partsContainer.querySelectorAll('.part-row');

    // Activer tous les boutons de suppression
    partRows.forEach(row => {
        const removeButton = row.querySelector('.remove-part');
        if (removeButton) {
            removeButton.disabled = false;
        }
    });

    // Créer une nouvelle ligne
    const newRow = document.createElement('div');
    newRow.className = 'row part-row mb-2';
    newRow.innerHTML = `
        <div class="col-md-6">
            <input type="text" class="form-control" name="parts[]" placeholder="Nom de la pièce" required>
        </div>
        <div class="col-md-2">
            <input type="number" class="form-control" name="quantities[]" placeholder="Qté" min="1" value="1">
        </div>
        <div class="col-md-3">
            <div class="input-group">
                <div class="input-group-prepend">
                    <span class="input-group-text">DT</span>
                </div>
                <input type="text" class="form-control" name="prices[]" placeholder="Prix estimé">
            </div>
        </div>
        <div class="col-md-1">
            <button type="button" class="btn btn-danger btn-sm remove-part">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    // Ajouter la nouvelle ligne au conteneur
    partsContainer.appendChild(newRow);

    // Ajouter l'écouteur d'événement pour le bouton de suppression
    const removeButton = newRow.querySelector('.remove-part');
    removeButton.addEventListener('click', removePartRow);
}

// Supprimer une ligne de pièce détachée
function removePartRow(event) {
    const button = event.target.closest('.remove-part');
    const row = button.closest('.part-row');
    const partsContainer = document.getElementById('parts-container');
    const partRows = partsContainer.querySelectorAll('.part-row');

    // Ne pas supprimer la dernière ligne
    if (partRows.length <= 1) {
        return;
    }

    // Supprimer la ligne
    row.remove();

    // Si une seule ligne reste, désactiver son bouton de suppression
    if (partRows.length === 2) {
        const lastRemoveButton = partsContainer.querySelector('.remove-part');
        if (lastRemoveButton) {
            lastRemoveButton.disabled = true;
        }
    }
}

// Enregistrer les photos avant réparation automatiquement
// Variable pour éviter les téléchargements en double
let isUploadingPhotos = false;

function savePhotos() {
    console.log('Fonction savePhotos appelée');

    // Ajouter des logs pour le débogage
    console.log('État de isUploadingPhotos avant appel:', isUploadingPhotos);
    console.log('Élément photo-upload:', document.getElementById('photo-upload'));
    console.log('Élément photo-before-form:', document.getElementById('photo-before-form'));
    console.log('Élément photo-notes:', document.getElementById('photo-notes'));
    console.log('Élément photo-preview-container:', document.getElementById('photo-preview-container'));

    // Vérifier si l'élément photo-upload contient des fichiers
    const photoUpload = document.getElementById('photo-upload');
    if (photoUpload && photoUpload.files) {
        console.log('Nombre de fichiers sélectionnés:', photoUpload.files.length);
        for (let i = 0; i < photoUpload.files.length; i++) {
            console.log(`Fichier ${i+1}:`, photoUpload.files[i].name, photoUpload.files[i].size, photoUpload.files[i].type);
        }
    }

    // Méthode alternative: soumettre directement le formulaire
    const form = document.getElementById('photo-before-form');
    if (form) {
        console.log('Soumission directe du formulaire photo-before-form');

        // Créer un FormData à partir du formulaire
        const formData = new FormData(form);

        // Afficher le contenu du FormData pour le débogage
        console.log('Contenu du FormData:');
        for (let pair of formData.entries()) {
            console.log(pair[0] + ': ' + (pair[1] instanceof File ? pair[1].name : pair[1]));
        }

        // Afficher un indicateur de chargement
        showAlert('Téléchargement des photos avant réparation en cours...', 'info');

        // Envoyer les données via AJAX
        fetch('upload_photos.php', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('Réponse du serveur (status):', response.status);
            return response.text().then(text => {
                console.log('Réponse brute du serveur:', text);
                try {
                    return JSON.parse(text);
                } catch (e) {
                    console.error('Réponse non-JSON:', text);
                    console.error('Erreur de parsing:', e);
                    throw new Error('Réponse invalide du serveur: ' + text.substring(0, 100));
                }
            });
        })
        .then(data => {
            console.log('Données reçues:', data);
            if (data.success) {
                showAlert(data.message, 'success');

                // Ajouter les nouvelles photos à la galerie
                if (data.photos && data.photos.length > 0) {
                    const photoContainer = document.getElementById('photo-preview-container');
                    if (photoContainer) {
                        // Supprimer le message "Aucune photo" s'il existe
                        if (photoContainer.textContent.includes('Aucune photo')) {
                            photoContainer.innerHTML = '';
                        }

                        // Ajouter chaque nouvelle photo à la galerie
                        data.photos.forEach(photo => {
                            const photoElement = document.createElement('div');
                            photoElement.className = 'col-md-4 col-sm-6 mb-4';
                            photoElement.setAttribute('data-photo-id', photo.id);
                            photoElement.innerHTML = `
                                <div class="card h-100 position-relative">
                                    <button type="button" class="btn-delete-photo" onclick="removeExistingPhoto(${photo.id})" title="Supprimer cette photo">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    <img src="${photo.file_path}" class="card-img-top" alt="${photo.file_name}">
                                    <div class="card-body">
                                        <p class="card-text small text-muted">${photo.file_name}</p>
                                        <p class="card-text small">${photo.upload_date ? new Date(photo.upload_date).toLocaleString() : ''}</p>
                                    </div>
                                </div>
                            `;
                            photoContainer.appendChild(photoElement);
                        });
                    }
                }

                // Réinitialiser le formulaire
                if (photoUpload) {
                    photoUpload.value = '';
                    const fileLabel = document.querySelector('.custom-file-label');
                    if (fileLabel) {
                        fileLabel.textContent = 'Choisir des fichiers';
                    }
                }
            } else {
                showAlert('Erreur: ' + (data.error || 'Erreur inconnue'), 'danger');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            showAlert('Erreur lors du téléchargement des photos: ' + error.message, 'danger');
        });

        return true;
    }

    // Fallback: utiliser la fonction générique pour télécharger les photos
    console.log('Fallback: utilisation de la fonction uploadPhotos');
    return uploadPhotos({
        inputId: 'photo-upload',
        formId: 'photo-before-form',
        notesId: 'photo-notes',
        containerId: 'photo-preview-container',
        uploadUrl: 'upload_photos.php',
        stageId: 2,
        isUploading: isUploadingPhotos,
        setIsUploading: (value) => {
            console.log('Mise à jour de isUploadingPhotos:', value);
            isUploadingPhotos = value;
        },
        alertMessage: 'Téléchargement des photos avant réparation en cours...',
        successLabelText: 'Choisir des fichiers'
    });
}

// Enregistrer les photos après réparation automatiquement
// Variable pour éviter les téléchargements en double
let isUploadingAfterPhotos = false;

function saveAfterPhotos() {
    console.log('Fonction saveAfterPhotos appelée');

    // Vérifier si l'élément after-photos contient des fichiers
    const photoUpload = document.getElementById('after-photos');
    if (photoUpload && photoUpload.files) {
        console.log('Nombre de fichiers sélectionnés:', photoUpload.files.length);
        for (let i = 0; i < photoUpload.files.length; i++) {
            console.log(`Fichier ${i+1}:`, photoUpload.files[i].name, photoUpload.files[i].size, photoUpload.files[i].type);
        }
    }

    // Méthode alternative: soumettre directement le formulaire
    const form = document.getElementById('upload-after-photos-form');
    if (form) {
        console.log('Soumission directe du formulaire upload-after-photos-form');

        // Créer un FormData à partir du formulaire
        const formData = new FormData(form);

        // Afficher le contenu du FormData pour le débogage
        console.log('Contenu du FormData:');
        for (let pair of formData.entries()) {
            console.log(pair[0] + ': ' + (pair[1] instanceof File ? pair[1].name : pair[1]));
        }

        // Afficher un indicateur de chargement
        showAlert('Téléchargement des photos après réparation en cours...', 'info');

        // Envoyer les données via AJAX
        fetch('upload_after_photos.php', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('Réponse du serveur (status):', response.status);
            return response.text().then(text => {
                console.log('Réponse brute du serveur:', text);
                try {
                    return JSON.parse(text);
                } catch (e) {
                    console.error('Réponse non-JSON:', text);
                    console.error('Erreur de parsing:', e);
                    throw new Error('Réponse invalide du serveur: ' + text.substring(0, 100));
                }
            });
        })
        .then(data => {
            console.log('Données reçues:', data);
            if (data.success) {
                showAlert(data.message, 'success');

                // Ajouter les nouvelles photos à la galerie
                if (data.photos && data.photos.length > 0) {
                    const photoContainer = document.getElementById('after-photos-gallery');
                    if (photoContainer) {
                        // Supprimer le message "Aucune photo" s'il existe
                        if (photoContainer.textContent.includes('Aucune photo')) {
                            photoContainer.innerHTML = '';
                        }

                        // Ajouter chaque nouvelle photo à la galerie
                        data.photos.forEach(photo => {
                            const photoElement = document.createElement('div');
                            photoElement.className = 'col-md-4 col-sm-6 mb-4';
                            photoElement.setAttribute('data-photo-id', photo.id);
                            photoElement.innerHTML = `
                                <div class="card h-100 position-relative">
                                    <button type="button" class="btn-delete-photo" onclick="removeExistingPhoto(${photo.id})" title="Supprimer cette photo">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    <img src="${photo.file_path}" class="card-img-top" alt="${photo.file_name}">
                                    <div class="card-body">
                                        <p class="card-text small text-muted">${photo.file_name}</p>
                                        <p class="card-text small">${photo.upload_date ? new Date(photo.upload_date).toLocaleString() : ''}</p>
                                    </div>
                                </div>
                            `;
                            photoContainer.appendChild(photoElement);
                        });
                    }
                }

                // Réinitialiser le formulaire
                if (photoUpload) {
                    photoUpload.value = '';
                    const fileLabel = document.querySelector('.custom-file-label');
                    if (fileLabel) {
                        fileLabel.textContent = 'Choisir des fichiers...';
                    }
                }
            } else {
                showAlert('Erreur: ' + (data.error || 'Erreur inconnue'), 'danger');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            showAlert('Erreur lors du téléchargement des photos: ' + error.message, 'danger');
        });

        return true;
    }

    // Fallback: utiliser la fonction générique pour télécharger les photos
    console.log('Fallback: utilisation de la fonction uploadPhotos');
    return uploadPhotos({
        inputId: 'after-photos',
        formId: 'upload-after-photos-form',
        notesId: 'after-photos-notes',
        containerId: 'after-photos-gallery',
        uploadUrl: 'upload_after_photos.php',
        stageId: 8,
        isUploading: isUploadingAfterPhotos,
        setIsUploading: (value) => { isUploadingAfterPhotos = value; },
        alertMessage: 'Téléchargement des photos après réparation en cours...',
        successLabelText: 'Choisir des fichiers...'
    });
}

// Terminer l'étape actuelle
function completeStage() {
    // Vérifier si des photos ont été téléchargées
    const photoContainer = document.getElementById('photo-preview-container');
    if (!photoContainer || photoContainer.children.length === 0) {
        showAlert('Veuillez télécharger au moins une photo avant de terminer cette étape', 'warning');
        return false;
    }

    // Demander confirmation
    if (!confirm('Êtes-vous sûr de vouloir terminer cette étape ? Cette action est irréversible.')) {
        return false;
    }

    // Le formulaire HTML s'occupe de l'envoi des données
    return true;
}

// Ces fonctions ne sont plus nécessaires car les filtres sont gérés par le formulaire HTML
// Nous les gardons vides pour éviter les erreurs si elles sont appelées ailleurs
function applyFilters() {
    console.log('Les filtres sont maintenant gérés par le formulaire HTML');
    // Le formulaire HTML s'occupe d'appliquer les filtres
}

function resetFilters() {
    console.log('Les filtres sont maintenant gérés par le formulaire HTML');
    // Le lien de réinitialisation s'occupe de réinitialiser les filtres
}

// Obtenir la couleur en fonction du statut
function getStatusColor(status) {
    switch (status) {
        case 'en_attente': return 'warning';
        case 'en_cours': return 'primary';
        case 'termine': return 'success';
        case 'annule': return 'danger';
        default: return 'secondary';
    }
}

// Obtenir le libellé en fonction du statut
function getStatusLabel(status) {
    switch (status) {
        case 'en_attente': return 'En attente';
        case 'en_cours': return 'En cours';
        case 'termine': return 'Terminé';
        case 'annule': return 'Annulé';
        default: return 'Inconnu';
    }
}

// Obtenir la couleur en fonction de l'étape
function getStageColor(stageId) {
    switch (stageId) {
        case 1: return 'secondary'; // Rendez-vous
        case 2: return 'purple';    // Photos avant
        case 3: return 'primary';   // Évaluation
        case 4: return 'warning';   // Pièces
        case 5: return 'primary';   // Réparation
        case 6: return 'info';      // Contrôle
        case 7: return 'info';      // Nettoyage
        case 8: return 'info';      // Photos après
        case 9: return 'warning';   // Livraison
        case 10: return 'success';  // Facturation
        default: return 'secondary';
    }
}

// Sélectionner ou désélectionner toutes les pièces
function toggleAllParts(event) {
    const isChecked = event.target.checked;
    const checkboxes = document.querySelectorAll('input[name="part_ids[]"]');

    checkboxes.forEach(checkbox => {
        checkbox.checked = isChecked;
    });
}

// Mettre à jour la couleur de fond du sélecteur de statut
function updateStatusColor(event) {
    const select = event.target;
    const value = select.value;

    // Supprimer toutes les classes de couleur
    select.classList.remove('bg-danger', 'bg-warning', 'bg-success');

    // Ajouter la classe appropriée
    if (value === 'en_attente' || value === 'pending') {
        select.classList.add('bg-danger');
    } else if (value === 'commande' || value === 'ordered') {
        select.classList.add('bg-warning');
    } else if (value === 'recu' || value === 'received') {
        select.classList.add('bg-success');
    }
}

// Charger les pièces requises pour un rendez-vous
function loadRequiredParts(rdvId) {
    // URL de l'API avec timestamp pour éviter le cache
    const url = `get_required_parts.php?rdv_id=${rdvId}&t=${new Date().getTime()}`;

    console.log('URL de l\'API:', url);

    console.log('Chargement des pièces pour le rendez-vous:', rdvId);

    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`Erreur réseau: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Réponse de get_required_parts.php:', data);

            if (data.success) {
                displayRequiredParts(data.parts, rdvId);
            } else {
                const partsTableBody = document.getElementById('parts-table-body');
                partsTableBody.innerHTML = `
                    <tr>
                        <td colspan="6" class="text-center text-danger">
                            <i class="fas fa-exclamation-circle mr-2"></i>${data.error || 'Erreur lors du chargement des pièces'}
                        </td>
                    </tr>
                `;
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            const partsTableBody = document.getElementById('parts-table-body');
            partsTableBody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center text-danger">
                        <i class="fas fa-exclamation-circle mr-2"></i>Erreur de connexion au serveur: ${error.message}
                    </td>
                </tr>
            `;
        });
}

// Afficher les pièces requises dans le tableau
function displayRequiredParts(parts, rdvId) {
    const partsTableBody = document.getElementById('parts-table-body');

    console.log('Affichage des pièces:', parts);
    console.log('Élément parts-table-body:', partsTableBody);

    if (!partsTableBody) {
        console.error('Élément parts-table-body non trouvé dans le DOM');
        return;
    }

    if (!parts || parts.length === 0) {
        partsTableBody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center">
                    <i class="fas fa-info-circle mr-2"></i>Aucune pièce n'a été définie pour cette réparation.
                </td>
            </tr>
        `;

        // Désactiver le bouton de confirmation
        const completeButton = document.getElementById('complete-parts-stage-btn');
        if (completeButton) {
            completeButton.disabled = true;
        } else {
            console.error('Élément complete-parts-stage-btn non trouvé dans le DOM');
        }
        return;
    }

    // Vider le tableau
    partsTableBody.innerHTML = '';

    // Compter les pièces reçues
    let receivedCount = 0;

    // Ajouter chaque pièce au tableau
    parts.forEach(part => {
        // Déterminer la classe de couleur pour le statut
        let statusClass = '';
        let status = '';

        // Vérifier si nous avons le nouveau format avec 'status' ou l'ancien format avec 'ordered' et 'received'
        if (part.status !== undefined) {
            // Nouveau format avec 'status'
            status = part.status;

            console.log('Pièce (nouveau format):', part.part_name, 'Status:', status);

            if (status === 'recu') {
                statusClass = 'success';
                receivedCount++;
            } else if (status === 'commande') {
                statusClass = 'warning';
            } else {
                statusClass = 'danger';
            }
        } else {
            // Ancien format avec 'ordered' et 'received'
            const received = part.received === '1' || part.received === 1;
            const ordered = part.ordered === '1' || part.ordered === 1;

            console.log('Pièce (ancien format):', part.part_name, 'Commandée:', ordered, 'Reçue:', received);

            if (received) {
                statusClass = 'success';
                status = 'recu';
                receivedCount++;
            } else if (ordered) {
                statusClass = 'warning';
                status = 'commande';
            } else {
                statusClass = 'danger';
                status = 'en_attente';
            }
        }

        // Déterminer le prix à afficher (peut être price ou estimated_price selon le format)
        const price = part.price !== undefined ? part.price : (part.estimated_price !== undefined ? part.estimated_price : '-');

        // Déterminer le numéro de pièce à afficher
        const partNumber = part.part_number !== undefined ? part.part_number : '-';

        // Créer la ligne du tableau
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${part.part_name}</td>
            <td>${partNumber}</td>
            <td>${part.quantity}</td>
            <td>${price ? price + ' DT' : '-'}</td>
            <td>
                <select class="form-control form-control-sm bg-${statusClass} text-white part-status-select" data-part-id="${part.id}">
                    <option value="en_attente" ${status === 'en_attente' ? 'selected' : ''}>À commander</option>
                    <option value="commande" ${status === 'commande' ? 'selected' : ''}>Commandée</option>
                    <option value="recu" ${status === 'recu' ? 'selected' : ''}>Reçue</option>
                </select>
            </td>
            <td>
                <button type="button" class="btn btn-sm btn-outline-info view-part-btn" data-part-id="${part.id}" data-toggle="tooltip" title="Voir les détails">
                    <i class="fas fa-eye"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-danger delete-part-btn" data-part-id="${part.id}" data-toggle="tooltip" title="Supprimer">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;

        partsTableBody.appendChild(tr);
    });

    // Ajouter les écouteurs d'événements pour les sélecteurs de statut
    document.querySelectorAll('.part-status-select').forEach(select => {
        select.addEventListener('change', function() {
            updatePartStatus(this.dataset.partId, this.value);
        });
    });

    // Activer ou désactiver le bouton de confirmation en fonction du nombre de pièces reçues
    const completeButton = document.getElementById('complete-parts-stage-btn');
    if (completeButton) {
        if (receivedCount === parts.length) {
            completeButton.disabled = false;
        } else {
            completeButton.disabled = true;
        }
    } else {
        console.error('Élément complete-parts-stage-btn non trouvé dans le DOM');
    }
}

// Ajouter une nouvelle pièce requise
function addRequiredPart() {
    // Récupérer les valeurs du formulaire
    const rdvId = document.getElementById('add-part-rdv-id').value;
    const partName = document.getElementById('part-name').value.trim();
    const partNumber = document.getElementById('part-number').value.trim();
    const quantity = document.getElementById('part-quantity').value;
    const price = document.getElementById('part-price').value;
    const status = document.getElementById('part-status').value;
    const notes = document.getElementById('part-notes').value.trim();

    console.log('Ajout d\'une pièce pour le rendez-vous:', rdvId);
    console.log('Données:', { partName, partNumber, quantity, price, status, notes });

    // Valider les données
    if (!partName) {
        alert('Veuillez saisir un nom de pièce');
        return;
    }

    // Créer les données du formulaire
    const formData = new FormData();
    formData.append('rdv_id', rdvId);
    formData.append('part_name', partName);
    formData.append('part_number', partNumber);
    formData.append('quantity', quantity);
    formData.append('price', price);
    formData.append('status', status);
    formData.append('notes', notes);

    // Envoyer la requête
    fetch('add_required_part.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        console.log('Réponse de add_required_part.php:', data);

        if (data.success) {
            // Réinitialiser le formulaire
            document.getElementById('part-name').value = '';
            document.getElementById('part-number').value = '';
            document.getElementById('part-quantity').value = '1';
            document.getElementById('part-price').value = '';
            document.getElementById('part-notes').value = '';

            // Recharger la liste des pièces
            loadRequiredParts(rdvId);

            // Afficher un message de succès
            alert(data.message);
        } else {
            alert(data.error || 'Erreur lors de l\'ajout de la pièce');
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        alert('Erreur de connexion au serveur');
    });
}

// Terminer l'étape des pièces détachées
function completePartsStage(rdvId) {
    console.log('Complétion de l\'étape des pièces détachées pour le rendez-vous:', rdvId);

    // Demander confirmation
    if (!confirm('Êtes-vous sûr de vouloir confirmer la réception de toutes les pièces et passer à l\'étape suivante ? Cette action est irréversible.')) {
        return;
    }

    // Créer les données du formulaire
    const formData = new FormData();
    formData.append('action', 'complete_stage');
    formData.append('rdv_id', rdvId);
    formData.append('stage_id', 4);
    formData.append('stage_status', 'confirme');

    // Envoyer la requête
    fetch('atelier.php', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Erreur réseau: ${response.status}`);
        }
        // Rediriger vers la page atelier
        window.location.href = 'atelier.php?success=' + encodeURIComponent('Étape des pièces détachées terminée avec succès');
    })
    .catch(error => {
        console.error('Erreur:', error);
        alert('Erreur lors de la complétion de l\'étape: ' + error.message);
    });
}

// Afficher les pièces utilisées pour la réparation
function displayUsedParts(parts) {
    const usedPartsTableBody = document.getElementById('used-parts-table-body');

    if (!usedPartsTableBody) {
        console.error('Élément used-parts-table-body non trouvé dans le DOM');
        return;
    }

    console.log('Pièces disponibles pour la réparation:', parts);

    // Afficher toutes les pièces disponibles pour la réparation
    // Nous ne filtrons plus car nous voulons montrer toutes les pièces

    if (!parts || parts.length === 0) {
        usedPartsTableBody.innerHTML = `
            <tr>
                <td colspan="3" class="text-center">
                    <i class="fas fa-info-circle mr-2"></i>Aucune pièce disponible pour cette réparation.
                </td>
            </tr>
        `;
        return;
    }

    // Vider le tableau
    usedPartsTableBody.innerHTML = '';

    // Ajouter chaque pièce au tableau
    parts.forEach(part => {
        const tr = document.createElement('tr');

        // Déterminer le statut de la pièce
        let statusText = 'Disponible';
        let statusClass = 'text-success';

        // Vérifier les différentes propriétés possibles pour déterminer le statut
        if (part.received === '0' || part.received === 0) {
            statusText = 'En attente';
            statusClass = 'text-warning';
        } else if (part.status === 'en_attente') {
            statusText = 'En attente';
            statusClass = 'text-warning';
        } else if (part.ordered === '0' || part.ordered === 0) {
            statusText = 'Non commandée';
            statusClass = 'text-danger';
        }

        tr.innerHTML = `
            <td>${part.part_name || 'Pièce sans nom'}</td>
            <td>${part.quantity || 1}</td>
            <td class="${statusClass}"><i class="fas fa-circle mr-1"></i>${statusText}</td>
        `;
        usedPartsTableBody.appendChild(tr);
    });
}

// Charger les pièces requises pour la réparation
function loadRequiredPartsForRepair(rdvId) {
    // URL de l'API avec timestamp pour éviter le cache
    const url = `get_required_parts.php?rdv_id=${rdvId}&t=${new Date().getTime()}`;

    console.log('Chargement des pièces pour la réparation:', url);

    // Faire l'appel AJAX pour récupérer les pièces
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`Erreur réseau: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Réponse de get_required_parts.php:', data);
            if (data.success) {
                if (data.parts && data.parts.length > 0) {
                    displayUsedParts(data.parts);
                } else {
                    console.log('Aucune pièce trouvée pour ce rendez-vous');
                    const usedPartsTableBody = document.getElementById('used-parts-table-body');
                    if (usedPartsTableBody) {
                        usedPartsTableBody.innerHTML = `
                            <tr>
                                <td colspan="3" class="text-center">
                                    <i class="fas fa-info-circle mr-2"></i>Aucune pièce disponible pour cette réparation.
                                    <br><a href="add_test_parts.php?rdv_id=${rdvId}" target="_blank" class="btn btn-sm btn-primary mt-2">
                                        <i class="fas fa-plus-circle mr-1"></i>Ajouter des pièces de test
                                    </a>
                                </td>
                            </tr>
                        `;
                    }
                }
            } else {
                console.error('Erreur lors du chargement des pièces:', data.error);
                alert('Erreur lors du chargement des pièces: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors du chargement des pièces: ' + error.message);
        });
}

// Confirmer la complétion de la réparation
function confirmRepairCompletion() {
    // Les notes de réparation sont maintenant optionnelles

    // Demander confirmation
    if (!confirm('Êtes-vous sûr de vouloir confirmer que la réparation est terminée et passer à l\'étape suivante ? Cette action est irréversible.')) {
        return false;
    }

    return true;
}

// Mettre à jour le statut d'une pièce
function updatePartStatus(partId, status) {
    console.log('Mise à jour du statut de la pièce:', partId, 'vers', status);

    // SOLUTION RADICALE: Déterminer ordered et received directement à partir du statut
    // sans utiliser la colonne status

    // Déterminer les valeurs ordered et received en fonction du statut
    let ordered = 0;
    let received = 0;

    // Convertir le statut en valeurs ordered et received
    if (status === 'received' || status === 'recu' || status === 'reçu' || status === 'reçue') {
        // Pièce reçue
        ordered = 1;
        received = 1;
    } else if (status === 'ordered' || status === 'commande' || status === 'commandé' || status === 'commandée') {
        // Pièce commandée mais pas encore reçue
        ordered = 1;
        received = 0;
    } else {
        // Pièce en attente (ni commandée ni reçue)
        ordered = 0;
        received = 0;
    }

    console.log('Valeurs déterminées:', { status, ordered, received });

    // Créer les données du formulaire
    const formData = new FormData();
    formData.append('part_id', partId);
    formData.append('status', status); // Envoyer le statut original pour le débogage
    formData.append('ordered', ordered);
    formData.append('received', received);

    // Envoyer la requête
    fetch('update_part_status.php', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Erreur réseau: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Réponse de update_part_status.php:', data);

        if (data.success) {
            // Mettre à jour l'interface utilisateur
            const select = document.querySelector(`.part-status-select[data-part-id="${partId}"]`);
            if (select) {
                // Mettre à jour la classe de couleur
                select.className = select.className.replace(/bg-\w+/, `bg-${getStatusColorClass(status)}`);
            }

            // Vérifier si toutes les pièces ont été reçues
            if (data.all_parts_received) {
                const completeButton = document.getElementById('complete-parts-stage-btn');
                if (completeButton) {
                    completeButton.disabled = false;
                }
            } else {
                const completeButton = document.getElementById('complete-parts-stage-btn');
                if (completeButton) {
                    completeButton.disabled = true;
                }
            }

            // Afficher un message de succès
            showAlert('Statut de la pièce mis à jour avec succès', 'success');
        } else {
            showAlert(data.error || 'Erreur lors de la mise à jour du statut', 'danger');
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        showAlert('Erreur de connexion au serveur: ' + error.message, 'danger');
    });
}

// Obtenir la classe de couleur en fonction du statut
function getStatusColorClass(status) {
    switch (status) {
        case 'recu':
            return 'success';
        case 'commande':
            return 'warning';
        default:
            return 'danger';
    }
}

// Cette fonction a été déplacée plus haut dans le code

// Afficher une alerte
function showAlert(message, type = 'info') {
    // Créer l'élément d'alerte
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.setAttribute('role', 'alert');
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    `;

    // Ajouter l'alerte au conteneur
    const alertContainer = document.getElementById('alert-container');
    if (alertContainer) {
        alertContainer.appendChild(alertDiv);

        // Supprimer l'alerte après 5 secondes
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    } else {
        // Fallback si le conteneur n'existe pas
        alert(`${type.toUpperCase()}: ${message}`);
    }
}

// Afficher des options alternatives pour les photos sur mobile (avant réparation)
function showMobilePhotoOptions() {
    const cameraContainer = document.getElementById('camera-container');
    if (!cameraContainer) return;

    // Créer un message d'information avec des options alternatives
    const messageDiv = document.createElement('div');
    messageDiv.className = 'alert alert-info';
    messageDiv.innerHTML = `
        <h5><i class="fas fa-info-circle mr-2"></i>Accès à la caméra impossible</h5>
        <p>Votre navigateur ne permet pas d'accéder directement à la caméra. Vous pouvez :</p>
        <ol>
            <li>Utiliser l'option "Choisir des fichiers" pour sélectionner des photos depuis votre galerie</li>
            <li>Prendre des photos avec l'application appareil photo de votre téléphone, puis les sélectionner</li>
        </ol>
        <div class="mt-3">
            <button type="button" class="btn btn-primary" id="mobile-photo-select-btn">
                <i class="fas fa-images mr-2"></i>Sélectionner des photos
            </button>
        </div>
    `;

    // Remplacer le contenu du conteneur de la caméra
    cameraContainer.innerHTML = '';
    cameraContainer.appendChild(messageDiv);
    cameraContainer.style.display = 'block';

    // Ajouter un écouteur d'événement pour le bouton de sélection de photos
    const selectBtn = document.getElementById('mobile-photo-select-btn');
    if (selectBtn) {
        selectBtn.addEventListener('click', function() {
            const photoInput = document.getElementById('photo-upload');
            if (photoInput) {
                photoInput.click();
            }
            cameraContainer.style.display = 'none';
        });
    }
}

// Afficher des options alternatives pour les photos sur mobile (après réparation)
function showMobileAfterPhotoOptions() {
    const cameraContainer = document.getElementById('after-camera-container');
    if (!cameraContainer) return;

    // Créer un message d'information avec des options alternatives
    const messageDiv = document.createElement('div');
    messageDiv.className = 'alert alert-info';
    messageDiv.innerHTML = `
        <h5><i class="fas fa-info-circle mr-2"></i>Accès à la caméra impossible</h5>
        <p>Votre navigateur ne permet pas d'accéder directement à la caméra. Vous pouvez :</p>
        <ol>
            <li>Utiliser l'option "Choisir des fichiers" pour sélectionner des photos depuis votre galerie</li>
            <li>Prendre des photos avec l'application appareil photo de votre téléphone, puis les sélectionner</li>
        </ol>
        <div class="mt-3">
            <button type="button" class="btn btn-primary" id="mobile-after-photo-select-btn">
                <i class="fas fa-images mr-2"></i>Sélectionner des photos
            </button>
        </div>
    `;

    // Remplacer le contenu du conteneur de la caméra
    cameraContainer.innerHTML = '';
    cameraContainer.appendChild(messageDiv);
    cameraContainer.style.display = 'block';

    // Ajouter un écouteur d'événement pour le bouton de sélection de photos
    const selectBtn = document.getElementById('mobile-after-photo-select-btn');
    if (selectBtn) {
        selectBtn.addEventListener('click', function() {
            const photoInput = document.getElementById('after-photos');
            if (photoInput) {
                photoInput.click();
            }
            cameraContainer.style.display = 'none';
        });
    }
}

// Variables pour la gestion de la caméra
let stream = null;
let afterStream = null;

// Initialiser la caméra pour les photos avant réparation
function initCamera() {
    const cameraContainer = document.getElementById('camera-container');
    const videoElement = document.getElementById('camera-preview');

    if (!cameraContainer || !videoElement) {
        console.error('Éléments de caméra non trouvés');
        return;
    }

    // Afficher le conteneur de la caméra
    cameraContainer.style.display = 'block';
    videoElement.style.display = 'block';

    // Vérifier si l'API MediaDevices est disponible
    if (!navigator.mediaDevices) {
        // Fallback pour les navigateurs plus anciens (notamment sur mobile)
        if (navigator.getUserMedia) {
            // Ancien navigateur
            navigator.getUserMedia = navigator.getUserMedia ||
                                    navigator.webkitGetUserMedia ||
                                    navigator.mozGetUserMedia ||
                                    navigator.msGetUserMedia;

            navigator.getUserMedia(
                { video: { facingMode: 'environment' } },
                function(stream) {
                    window.stream = stream;
                    videoElement.srcObject = stream;
                    videoElement.play();
                },
                function(error) {
                    console.error('Erreur lors de l\'accès à la caméra (ancien navigateur):', error);
                    showMobilePhotoOptions();
                    cameraContainer.style.display = 'none';
                }
            );
        } else {
            console.error('Votre navigateur ne prend pas en charge l\'accès à la caméra');
            showMobilePhotoOptions();
            cameraContainer.style.display = 'none';
        }
        return;
    }

    // Pour les navigateurs modernes
    navigator.mediaDevices.getUserMedia({
        video: {
            facingMode: 'environment', // Utiliser la caméra arrière si disponible
            width: { ideal: 1280 },
            height: { ideal: 720 }
        }
    })
    .then(function(mediaStream) {
        stream = mediaStream;
        videoElement.srcObject = mediaStream;
        videoElement.play();
    })
    .catch(function(error) {
        console.error('Erreur lors de l\'accès à la caméra:', error);

        // Au lieu d'afficher une alerte, proposer une alternative
        showMobilePhotoOptions();
        cameraContainer.style.display = 'none';
    });
}

// Capturer une photo depuis la caméra (avant réparation)
function capturePhoto() {
    const videoElement = document.getElementById('camera-preview');
    const canvasElement = document.getElementById('camera-canvas');
    const previewContainer = document.getElementById('photo-preview-container');

    if (!videoElement || !canvasElement || !previewContainer) {
        console.error('Éléments nécessaires non trouvés');
        return;
    }

    // Configurer le canvas pour correspondre à la taille de la vidéo
    canvasElement.width = videoElement.videoWidth;
    canvasElement.height = videoElement.videoHeight;

    // Dessiner l'image de la vidéo sur le canvas
    const context = canvasElement.getContext('2d');
    context.drawImage(videoElement, 0, 0, canvasElement.width, canvasElement.height);

    // Convertir le canvas en image
    const imageDataUrl = canvasElement.toDataURL('image/jpeg');

    // Générer un ID unique pour cette photo
    const photoId = 'photo_' + new Date().getTime();

    // Créer un élément d'aperçu pour l'image capturée
    const previewItem = document.createElement('div');
    previewItem.className = 'photo-preview-item';
    previewItem.dataset.photoId = photoId;

    const img = document.createElement('img');
    img.src = imageDataUrl;
    img.alt = 'Photo capturée';

    const removeBtn = document.createElement('div');
    removeBtn.className = 'remove-photo';
    removeBtn.innerHTML = '<i class="fas fa-times"></i>';

    previewItem.appendChild(img);
    previewItem.appendChild(removeBtn);
    previewContainer.appendChild(previewItem);

    // Convertir l'image en fichier et l'ajouter à l'input file
    canvasElement.toBlob(function(blob) {
        const file = new File([blob], photoId + ".jpg", { type: "image/jpeg" });

        // Créer un objet DataTransfer pour manipuler l'input file
        const dataTransfer = new DataTransfer();

        // Ajouter les fichiers existants
        const fileInput = document.getElementById('photo-upload');
        if (fileInput.files.length > 0) {
            for (let i = 0; i < fileInput.files.length; i++) {
                dataTransfer.items.add(fileInput.files[i]);
            }
        }

        // Ajouter le nouveau fichier
        dataTransfer.items.add(file);

        // Mettre à jour l'input file
        fileInput.files = dataTransfer.files;

        // Mettre à jour le label
        const fileLabel = document.querySelector('.custom-file-label[for="photo-upload"]');
        if (fileLabel) {
            fileLabel.textContent = fileInput.files.length > 1 ? `${fileInput.files.length} fichiers sélectionnés` : fileInput.files[0].name;
        }

        // Ajouter un écouteur d'événement pour le bouton de suppression
        removeBtn.addEventListener('click', function() {
            // Supprimer l'élément de prévisualisation
            previewItem.remove();

            // Supprimer le fichier de l'input file
            const newDataTransfer = new DataTransfer();

            // Recréer la liste de fichiers sans le fichier supprimé
            for (let i = 0; i < fileInput.files.length; i++) {
                if (fileInput.files[i].name !== photoId + ".jpg") {
                    newDataTransfer.items.add(fileInput.files[i]);
                }
            }

            // Mettre à jour l'input file
            fileInput.files = newDataTransfer.files;

            // Mettre à jour le label
            if (fileLabel) {
                if (fileInput.files.length > 1) {
                    fileLabel.textContent = `${fileInput.files.length} fichiers sélectionnés`;
                } else if (fileInput.files.length === 1) {
                    fileLabel.textContent = fileInput.files[0].name;
                } else {
                    fileLabel.textContent = 'Choisir des fichiers';
                }
            }
        });
    }, 'image/jpeg', 0.9);

    // Fermer la caméra
    stopCamera();
}

// Arrêter la caméra
function stopCamera() {
    const cameraContainer = document.getElementById('camera-container');
    const videoElement = document.getElementById('camera-preview');

    if (cameraContainer) {
        cameraContainer.style.display = 'none';
    }

    if (videoElement) {
        videoElement.style.display = 'none';
        videoElement.pause();
        videoElement.srcObject = null;
    }

    // Arrêter tous les tracks du stream
    if (stream) {
        stream.getTracks().forEach(track => track.stop());
        stream = null;
    }
}

// Initialiser la caméra pour les photos après réparation
function initAfterCamera() {
    const cameraContainer = document.getElementById('after-camera-container');
    const videoElement = document.getElementById('after-camera-preview');

    if (!cameraContainer || !videoElement) {
        console.error('Éléments de caméra après réparation non trouvés');
        return;
    }

    // Afficher le conteneur de la caméra
    cameraContainer.style.display = 'block';
    videoElement.style.display = 'block';

    // Vérifier si l'API MediaDevices est disponible
    if (!navigator.mediaDevices) {
        // Fallback pour les navigateurs plus anciens (notamment sur mobile)
        if (navigator.getUserMedia) {
            // Ancien navigateur
            navigator.getUserMedia = navigator.getUserMedia ||
                                    navigator.webkitGetUserMedia ||
                                    navigator.mozGetUserMedia ||
                                    navigator.msGetUserMedia;

            navigator.getUserMedia(
                { video: { facingMode: 'environment' } },
                function(stream) {
                    window.afterStream = stream;
                    videoElement.srcObject = stream;
                    videoElement.play();
                },
                function(error) {
                    console.error('Erreur lors de l\'accès à la caméra (ancien navigateur):', error);
                    showMobileAfterPhotoOptions();
                    cameraContainer.style.display = 'none';
                }
            );
        } else {
            console.error('Votre navigateur ne prend pas en charge l\'accès à la caméra');
            showMobileAfterPhotoOptions();
            cameraContainer.style.display = 'none';
        }
        return;
    }

    // Pour les navigateurs modernes
    navigator.mediaDevices.getUserMedia({
        video: {
            facingMode: 'environment', // Utiliser la caméra arrière si disponible
            width: { ideal: 1280 },
            height: { ideal: 720 }
        }
    })
    .then(function(mediaStream) {
        afterStream = mediaStream;
        videoElement.srcObject = mediaStream;
        videoElement.play();
    })
    .catch(function(error) {
        console.error('Erreur lors de l\'accès à la caméra:', error);

        // Au lieu d'afficher une alerte, proposer une alternative
        showMobileAfterPhotoOptions();
        cameraContainer.style.display = 'none';
    });
}

// Capturer une photo depuis la caméra (après réparation)
function captureAfterPhoto() {
    const videoElement = document.getElementById('after-camera-preview');
    const canvasElement = document.getElementById('after-camera-canvas');
    const galleryContainer = document.getElementById('after-photos-gallery');

    if (!videoElement || !canvasElement || !galleryContainer) {
        console.error('Éléments nécessaires non trouvés');
        return;
    }

    // Configurer le canvas pour correspondre à la taille de la vidéo
    canvasElement.width = videoElement.videoWidth;
    canvasElement.height = videoElement.videoHeight;

    // Dessiner l'image de la vidéo sur le canvas
    const context = canvasElement.getContext('2d');
    context.drawImage(videoElement, 0, 0, canvasElement.width, canvasElement.height);

    // Convertir le canvas en image
    const imageDataUrl = canvasElement.toDataURL('image/jpeg');

    // Générer un ID unique pour cette photo
    const photoId = 'photo_' + new Date().getTime();

    // Créer un élément d'aperçu pour l'image capturée
    const colDiv = document.createElement('div');
    colDiv.className = 'col-md-4 col-sm-6 mb-4';
    colDiv.id = photoId;

    const cardDiv = document.createElement('div');
    cardDiv.className = 'card h-100';

    const img = document.createElement('img');
    img.src = imageDataUrl;
    img.className = 'card-img-top';
    img.alt = 'Photo capturée';

    const cardBodyDiv = document.createElement('div');
    cardBodyDiv.className = 'card-body';

    const fileNameP = document.createElement('p');
    fileNameP.className = 'card-text small text-muted';
    fileNameP.textContent = 'Photo_' + new Date().toLocaleString();

    // Ajouter un bouton de suppression
    const deleteBtn = document.createElement('button');
    deleteBtn.className = 'btn btn-sm btn-danger mt-2';
    deleteBtn.innerHTML = '<i class="fas fa-trash mr-1"></i> Supprimer';
    deleteBtn.type = 'button';

    cardBodyDiv.appendChild(fileNameP);
    cardBodyDiv.appendChild(deleteBtn);
    cardDiv.appendChild(img);
    cardDiv.appendChild(cardBodyDiv);
    colDiv.appendChild(cardDiv);

    // Supprimer le message "Aucune photo" s'il existe
    if (galleryContainer.querySelector('.alert-warning')) {
        galleryContainer.innerHTML = '';
    }

    galleryContainer.appendChild(colDiv);

    // Convertir l'image en fichier et l'ajouter à l'input file
    canvasElement.toBlob(function(blob) {
        const file = new File([blob], photoId + ".jpg", { type: "image/jpeg" });

        // Créer un objet DataTransfer pour manipuler l'input file
        const dataTransfer = new DataTransfer();

        // Ajouter les fichiers existants
        const fileInput = document.getElementById('after-photos');
        if (fileInput.files.length > 0) {
            for (let i = 0; i < fileInput.files.length; i++) {
                dataTransfer.items.add(fileInput.files[i]);
            }
        }

        // Ajouter le nouveau fichier
        dataTransfer.items.add(file);

        // Mettre à jour l'input file
        fileInput.files = dataTransfer.files;

        // Mettre à jour le label
        const fileLabel = document.querySelector('.custom-file-label[for="after-photos"]');
        if (fileLabel) {
            fileLabel.textContent = fileInput.files.length > 1 ? `${fileInput.files.length} fichiers sélectionnés` : fileInput.files[0].name;
        }

        // Ajouter un écouteur d'événement pour le bouton de suppression
        deleteBtn.addEventListener('click', function() {
            // Supprimer l'élément de la galerie
            colDiv.remove();

            // Supprimer le fichier de l'input file
            const newDataTransfer = new DataTransfer();

            // Recréer la liste de fichiers sans le fichier supprimé
            for (let i = 0; i < fileInput.files.length; i++) {
                if (fileInput.files[i].name !== photoId + ".jpg") {
                    newDataTransfer.items.add(fileInput.files[i]);
                }
            }

            // Mettre à jour l'input file
            fileInput.files = newDataTransfer.files;

            // Mettre à jour le label
            if (fileLabel) {
                if (fileInput.files.length > 1) {
                    fileLabel.textContent = `${fileInput.files.length} fichiers sélectionnés`;
                } else if (fileInput.files.length === 1) {
                    fileLabel.textContent = fileInput.files[0].name;
                } else {
                    fileLabel.textContent = 'Choisir des fichiers...';

                    // Ajouter un message "Aucune photo" si la galerie est vide
                    if (galleryContainer.children.length === 0) {
                        galleryContainer.innerHTML = `
                            <div class="col-12">
                                <div class="alert alert-warning">
                                    Aucune photo après réparation n'a été téléchargée pour ce véhicule.
                                </div>
                            </div>
                        `;
                    }
                }
            }
        });
    }, 'image/jpeg', 0.9);

    // Fermer la caméra
    stopAfterCamera();
}

// Arrêter la caméra après réparation
function stopAfterCamera() {
    const cameraContainer = document.getElementById('after-camera-container');
    const videoElement = document.getElementById('after-camera-preview');

    if (cameraContainer) {
        cameraContainer.style.display = 'none';
    }

    if (videoElement) {
        videoElement.style.display = 'none';
        videoElement.pause();
        videoElement.srcObject = null;
    }

    // Arrêter tous les tracks du stream
    if (afterStream) {
        afterStream.getTracks().forEach(track => track.stop());
        afterStream = null;
    }
}

// Fonction pour générer une facture
function generateInvoice() {
    // Récupérer les données du formulaire
    const rdvId = document.getElementById('invoice-rdv-id').value;
    const invoiceNumber = document.getElementById('invoice-number').value;
    const invoiceNotes = document.getElementById('invoice-notes').value;
    const emailTo = document.getElementById('invoice-email').value;
    const completeStage = document.getElementById('complete-invoice-stage').checked ? 1 : 0;

    // Vérifier que les données sont valides
    if (!rdvId) {
        showAlert('Erreur: ID de rendez-vous manquant', 'danger');
        return;
    }

    // Afficher un indicateur de chargement
    const generateInvoiceBtn = document.getElementById('generate-invoice-btn');
    if (generateInvoiceBtn) {
        generateInvoiceBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Génération en cours...';
        generateInvoiceBtn.disabled = true;
    }

    // Préparer les données à envoyer
    const data = {
        rdv_id: rdvId,
        invoice_number: invoiceNumber,
        invoice_notes: invoiceNotes,
        email_to: emailTo,
        complete_stage: completeStage
    };

    // Afficher les données envoyées dans la console pour le débogage
    console.log('Données envoyées pour la génération du rapport:', data);

    // Envoyer la requête AJAX
    console.log('Tentative d\'utilisation du script de génération de rapport');

    // Utiliser le script original avec les modifications pour résoudre le problème de génération de PDF
    fetch('generate_invoice_ajax.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        console.log('Réponse du serveur (status):', response.status);

        // Vérifier si la réponse est OK
        if (!response.ok) {
            console.error('Erreur HTTP:', response.status, response.statusText);
            throw new Error('Erreur HTTP: ' + response.status);
        }

        // Essayer de parser la réponse JSON
        return response.text().then(text => {
            console.log('Réponse brute du serveur:', text);
            try {
                return JSON.parse(text);
            } catch (e) {
                console.error('Erreur de parsing JSON:', e);
                throw new Error('Réponse invalide du serveur');
            }
        });
    })
    .then(data => {
        console.log('Données reçues du serveur:', data);

        // Réactiver le bouton
        if (generateInvoiceBtn) {
            generateInvoiceBtn.innerHTML = '<i class="fas fa-file-pdf mr-2"></i>Générer le rapport';
            generateInvoiceBtn.disabled = false;
        }

        if (data.success) {
            // Afficher un message de succès
            showAlert(data.message || 'Rapport généré avec succès', 'success');

            // Ajouter un lien pour voir le rapport
            const invoiceForm = document.getElementById('invoice-form');
            if (invoiceForm && !document.getElementById('view-invoice-btn')) {
                const viewBtn = document.createElement('a');
                viewBtn.id = 'view-invoice-btn';
                viewBtn.className = 'btn btn-info ml-2';
                viewBtn.href = data.pdf_path;
                viewBtn.target = '_blank';
                viewBtn.innerHTML = '<i class="fas fa-eye mr-2"></i>Voir le rapport';

                // Ajouter le bouton à côté du bouton de génération
                const btnContainer = generateInvoiceBtn.parentNode;
                btnContainer.appendChild(viewBtn);
            } else if (document.getElementById('view-invoice-btn')) {
                // Mettre à jour le lien si le bouton existe déjà
                const viewBtn = document.getElementById('view-invoice-btn');
                viewBtn.href = data.pdf_path;
            }

            // Afficher un lien vers les logs pour le débogage
            console.log('Pour plus d\'informations, consultez les logs: test_report_form.php');

            // Si l'étape a été terminée, recharger la page après 2 secondes
            if (data.stage_completed) {
                setTimeout(() => {
                    location.reload();
                }, 2000);
            }
        } else {
            // Afficher un message d'erreur
            showAlert('Erreur: ' + (data.error || 'Erreur inconnue'), 'danger');
            console.error('Erreur lors de la génération du rapport:', data.error);
            console.log('Pour plus d\'informations, consultez les logs: test_report_form.php');
        }
    })
    .catch(error => {
        console.error('Erreur lors de la génération du rapport:', error);

        // Afficher un message d'erreur plus détaillé
        let errorMessage = 'Erreur de connexion au serveur';
        if (error.message) {
            errorMessage += ': ' + error.message;
        }

        showAlert(errorMessage, 'danger');

        // Réactiver le bouton
        if (generateInvoiceBtn) {
            generateInvoiceBtn.innerHTML = '<i class="fas fa-file-pdf mr-2"></i>Générer le rapport';
            generateInvoiceBtn.disabled = false;
        }

        // Suggérer des solutions
        console.log('Suggestions pour résoudre le problème:');
        console.log('1. Vérifiez que vous êtes bien connecté');
        console.log('2. Vérifiez que TCPDF est correctement installé en ouvrant test_pdf.php');
        console.log('3. Consultez les logs en ouvrant test_report_form.php');
        console.log('4. Essayez de générer un rapport simple en utilisant test_report_form.php');
    });
}




