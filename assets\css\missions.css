/* Styles pour la page des missions */

/* Barre de progression des étapes */
.progress-container {
    width: 100%;
    margin: 20px auto;
    position: relative;
}

.progressbar {
    counter-reset: step;
    padding: 0;
    display: flex;
    justify-content: space-between;
}

.progressbar li {
    list-style-type: none;
    position: relative;
    text-align: center;
    flex: 1;
    font-size: 12px;
    color: #7b7b7b;
    font-weight: 500;
    cursor: default;
}

.progressbar li:before {
    content: counter(step);
    counter-increment: step;
    width: 30px;
    height: 30px;
    line-height: 30px;
    border: 2px solid #ddd;
    border-radius: 50%;
    display: block;
    text-align: center;
    margin: 0 auto 10px auto;
    background-color: white;
    z-index: 2;
    position: relative;
    font-weight: bold;
}

.progressbar li:after {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    background-color: #ddd;
    top: 15px;
    left: -50%;
    z-index: 1;
}

.progressbar li:first-child:after {
    content: none;
}

.progressbar li.active {
    color: #4099ff;
}

.progressbar li.active:before {
    border-color: #4099ff;
    background-color: #4099ff;
    color: white;
}

.progressbar li.active:after {
    background-color: #4099ff;
}

.progressbar li.current:before {
    border-color: #4099ff;
    background-color: white;
    color: #4099ff;
    box-shadow: 0 0 0 3px rgba(64, 153, 255, 0.2);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(64, 153, 255, 0.4);
    }
    70% {
        box-shadow: 0 0 0 6px rgba(64, 153, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(64, 153, 255, 0);
    }
}

/* Styles pour les aperçus de photos */
#photo-preview-container {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.photo-preview-item {
    position: relative;
    width: 150px;
    height: 150px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.photo-preview-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.photo-preview-item .remove-photo {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 24px;
    height: 24px;
    background-color: rgba(255, 0, 0, 0.7);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.2s;
}

.photo-preview-item:hover .remove-photo {
    opacity: 1;
}

/* Styles pour les badges de statut */
.badge {
    padding: 5px 10px;
    font-weight: 500;
    font-size: 12px;
    border-radius: 4px;
}

.bg-warning {
    background-color: #ffb64d !important;
}

.bg-primary {
    background-color: #4099ff !important;
}

.bg-success {
    background-color: #2ed8b6 !important;
}

.bg-danger {
    background-color: #ff5370 !important;
}

.bg-info {
    background-color: #00acc1 !important;
}

/* Styles pour les cartes */
.card {
    border-radius: 5px;
    box-shadow: 0 1px 20px 0 rgba(69, 90, 100, 0.08);
    border: none;
    margin-bottom: 30px;
}

.card .card-header {
    background-color: transparent;
    border-bottom: 1px solid #f1f1f1;
    padding: 20px 25px;
}

.card .card-body {
    padding: 20px 25px;
}

.shadow-sm {
    box-shadow: 0 1px 15px rgba(0, 0, 0, 0.05) !important;
}

/* Styles pour les boutons */
.btn {
    border-radius: 4px;
    font-size: 14px;
    margin-bottom: 5px;
    margin-right: 10px;
    transition: all 0.3s ease-in-out;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 12px;
}

.btn-primary {
    background-color: #4099ff;
    border-color: #4099ff;
}

.btn-primary:hover {
    background-color: #0e7eff;
    border-color: #0e7eff;
}

.btn-info {
    background-color: #00acc1;
    border-color: #00acc1;
}

.btn-info:hover {
    background-color: #0095a8;
    border-color: #0095a8;
}

.btn-success {
    background-color: #2ed8b6;
    border-color: #2ed8b6;
}

.btn-success:hover {
    background-color: #21b69a;
    border-color: #21b69a;
}

/* Styles pour les formulaires */
.form-control {
    border-radius: 4px;
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #4099ff;
    box-shadow: 0 0 0 0.2rem rgba(64, 153, 255, 0.25);
}

.custom-file-label {
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
}

.custom-file-input:focus ~ .custom-file-label {
    border-color: #4099ff;
    box-shadow: 0 0 0 0.2rem rgba(64, 153, 255, 0.25);
}

/* Styles pour les tableaux */
.table {
    width: 100%;
    margin-bottom: 1rem;
    color: #212529;
}

.table th {
    padding: 1rem;
    vertical-align: top;
    border-top: 1px solid #e2e5e8;
    font-weight: 600;
}

.table td {
    padding: 1rem;
    vertical-align: middle;
    border-top: 1px solid #e2e5e8;
}

.table-hover tbody tr:hover {
    background-color: rgba(64, 153, 255, 0.05);
}

/* Styles pour les modals */
.modal-content {
    border: none;
    border-radius: 5px;
}

.modal-header {
    border-bottom: 1px solid #f1f1f1;
    padding: 15px 25px;
}

.modal-body {
    padding: 20px 25px;
}

.modal-footer {
    border-top: 1px solid #f1f1f1;
    padding: 15px 25px;
}

.modal-xl {
    max-width: 1140px;
}

/* Styles responsifs */
@media (max-width: 768px) {
    .progressbar li {
        font-size: 10px;
    }
    
    .progressbar li:before {
        width: 25px;
        height: 25px;
        line-height: 25px;
    }
    
    .progressbar li:after {
        top: 12px;
    }
    
    .modal-xl {
        max-width: 100%;
    }
}

@media (max-width: 576px) {
    .progressbar li {
        font-size: 0;
    }
    
    .progressbar li:before {
        width: 20px;
        height: 20px;
        line-height: 20px;
        font-size: 10px;
    }
    
    .progressbar li:after {
        top: 10px;
    }
}
