/* Styles pour la caméra */
#camera-container, #after-camera-container {
    margin-bottom: 20px;
    border-radius: 8px;
    overflow: hidden;
}

#camera-preview, #after-camera-preview {
    width: 100%;
    max-height: 400px;
    object-fit: cover;
    border-radius: 8px 8px 0 0;
}

#camera-canvas, #after-camera-canvas {
    display: none;
}

/* Styles pour les messages d'erreur de la caméra */
#camera-container .alert, #after-camera-container .alert {
    margin-bottom: 0;
    border-radius: 8px;
    padding: 15px 20px;
}

#camera-container .alert h5, #after-camera-container .alert h5 {
    font-weight: 600;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

#camera-container .alert ol, #after-camera-container .alert ol {
    margin-bottom: 15px;
    padding-left: 20px;
}

#camera-container .alert ol li, #after-camera-container .alert ol li {
    margin-bottom: 8px;
}

#mobile-photo-select-btn, #mobile-after-photo-select-btn {
    width: 100%;
    padding: 10px 15px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Styles pour les boutons de la caméra */
#capture-photo-btn, #capture-after-photo-btn {
    background-color: #28a745;
    border-color: #28a745;
    font-weight: 600;
    padding: 10px 20px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

#capture-photo-btn:hover, #capture-after-photo-btn:hover {
    background-color: #218838;
    border-color: #1e7e34;
    transform: scale(1.05);
}

#cancel-camera-btn, #cancel-after-camera-btn {
    background-color: #dc3545;
    border-color: #dc3545;
    font-weight: 600;
    padding: 10px 20px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

#cancel-camera-btn:hover, #cancel-after-camera-btn:hover {
    background-color: #c82333;
    border-color: #bd2130;
    transform: scale(1.05);
}

/* Styles pour les boutons de prise de photo */
#take-photo-btn, #take-after-photo-btn {
    background-color: #007bff;
    border-color: #007bff;
    font-weight: 600;
    padding: 12px 20px;
    border-radius: 4px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

#take-photo-btn:hover, #take-after-photo-btn:hover {
    background-color: #0069d9;
    border-color: #0062cc;
    transform: scale(1.05);
}

/* Styles pour les appareils mobiles */
@media (max-width: 768px) {
    #camera-preview, #after-camera-preview {
        max-height: 300px;
    }

    .photo-preview-item {
        width: 120px;
        height: 120px;
        margin: 5px;
    }
}
