<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('required_parts', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('damage_assessment_id');
            $table->unsignedInteger('rdv_id');
            $table->string('part_name', 200);
            $table->string('part_number', 100)->nullable();
            $table->integer('quantity')->default(1);
            $table->decimal('unit_price', 10, 2)->nullable();
            $table->decimal('total_price', 10, 2)->nullable();
            $table->string('supplier', 100)->nullable();
            $table->enum('status', ['needed', 'ordered', 'received', 'installed', 'cancelled'])->default('needed');
            $table->date('ordered_date')->nullable();
            $table->date('received_date')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();
            
            $table->foreign('damage_assessment_id')->references('id')->on('damage_assessments')->onDelete('cascade');
            $table->foreign('rdv_id')->references('id')->on('rdv')->onDelete('cascade');
            
            $table->index(['damage_assessment_id', 'rdv_id']);
            $table->index('status');
            $table->index('part_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('required_parts');
    }
};
