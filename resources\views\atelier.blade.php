@extends('layouts')

@section('content')
<div class="pcoded-main-container">
    <div class="pcoded-content">
        <!-- [ breadcrumb ] start -->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10">Atelier</h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ url('welcome') }}"><i class="fas fa-tools"></i></a></li>
                            <li class="breadcrumb-item"><a href="#!">Atelier</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!-- [ breadcrumb ] end -->
        <!-- [ Main Content ] start -->
        <!-- Container pour les alertes -->
        <div id="alert-container" class="mb-4">
            @if (session('success'))
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle mr-2"></i>
                    {{ session('success') }}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            @endif

            @if (session('error'))
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    {{ session('error') }}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            @endif
        </div>

        <div class="row">
            <!-- [ Filtres ] start -->
            <div class="col-sm-12 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Filtres</h5>
                        <div class="card-header-right">
                            <div class="btn-group card-option">
                                <button type="button" class="btn dropdown-toggle btn-icon" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="feather icon-more-horizontal"></i>
                                </button>
                                <ul class="list-unstyled card-option dropdown-menu dropdown-menu-right">
                                    <li class="dropdown-item minimize-card"><a href="#!"><span><i class="feather icon-minus"></i> collapse</span><span style="display:none"><i class="feather icon-plus"></i> expand</span></a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <form id="filter-form" method="get" action="{{ url('/atelier') }}">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <label for="filter-stage">Étape</label>
                                    <select class="form-control" id="filter-stage" name="stage" onchange="this.form.submit()">
                                        <option value="all" {{ $filters['stage'] === 'all' ? 'selected' : '' }}>Toutes les étapes</option>
                                        @foreach($stages as $stage)
                                            <option value="{{ $stage->id }}" {{ $filters['stage'] == $stage->id ? 'selected' : '' }}>
                                                {{ $stage->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="filter-date">Date</label>
                                    <input type="date" class="form-control" id="filter-date" name="date" value="{{ $filters['date'] }}" onchange="this.form.submit()">
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="filter-search">Recherche</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="filter-search" name="search" placeholder="Immatriculation, client..." value="{{ $filters['search'] }}">
                                        <div class="input-group-append">
                                            <button class="btn btn-primary" type="submit">
                                                <i class="fas fa-search"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3 d-flex align-items-end">
                                    <a href="{{ route('/atelier') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-undo mr-2"></i>Réinitialiser les filtres
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- [ Filtres ] end -->

            <!-- [ Liste des missions ] start -->
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Travaux en Atelier</h5>
                        <div class="card-header-right">
                            <div class="btn-group card-option">
                                <button type="button" class="btn dropdown-toggle btn-icon" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="feather icon-more-horizontal"></i>
                                </button>
                                <ul class="list-unstyled card-option dropdown-menu dropdown-menu-right">
                                    <li class="dropdown-item full-card"><a href="#!"><span><i class="feather icon-maximize"></i> maximize</span><span style="display:none"><i class="feather icon-minimize"></i> Restore</span></a></li>
                                    <li class="dropdown-item minimize-card"><a href="#!"><span><i class="feather icon-minus"></i> collapse</span><span style="display:none"><i class="feather icon-plus"></i> expand</span></a></li>
                                    <li class="dropdown-item reload-card"><a href="#!"><i class="feather icon-refresh-cw"></i> reload</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Client</th>
                                        <th>Véhicule</th>
                                        <th>Date RDV</th>
                                        <th>Étape actuelle</th>
                                        <th>Statut</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="missions-table-body">
                                    @forelse($missions as $mission)
                                        @php
                                            $currentStageId = $mission->current_stage_id ?? 1;
                                            $stageName = $mission->currentStage->name ?? 'Étape inconnue';
                                            $stageColor = $stageDefinitions[$currentStageId]['color'] ?? 'secondary';
                                            
                                            $stageStatus = $mission->latestStageStatus->status ?? 'en_attente';
                                            $stageStatusLabel = $stageStatus === 'confirme' ? 'Confirmé' : ($currentStageId == 1 ? 'En attente' : 'En cours');
                                            $stageStatusColor = $stageStatus === 'confirme' ? 'success' : ($currentStageId == 1 ? 'warning' : 'purple');
                                            
                                            $status = $mission->statut;
                                            $statusColor = $statusDefinitions[$status]['color'] ?? 'secondary';
                                            $statusLabel = $statusDefinitions[$status]['label'] ?? 'Inconnu';
                                            
                                            $formattedDate = $mission->jour_rdv->format('d/m/Y');
                                            $formattedTime = substr($mission->heure_rdv, 0, 5);
                                        @endphp
                                        <tr>
                                            <td>{{ $mission->id }}</td>
                                            <td>{{ $mission->car->client->nom }} {{ $mission->car->client->prenom }}</td>
                                            <td>{{ $mission->car->marque }} {{ $mission->car->modele }} ({{ $mission->car->immatriculation }})</td>
                                            <td>{{ $formattedDate }} {{ $formattedTime }}</td>
                                            <td><span class="badge bg-{{ $stageColor }} text-white">{{ $stageName }}</span></td>
                                            <td><span class="badge bg-{{ $stageStatusColor }} text-white">{{ $stageStatusLabel }}</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-primary" onclick="openMissionDetails({{ $mission->id }})">
                                                    <i class="fas fa-tasks mr-1"></i> Gérer
                                                </button>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr><td colspan="7" class="text-center">Aucun travail en atelier trouvé</td></tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- [ Liste des missions ] end -->
        </div>

        <!-- [ Modal pour la gestion des étapes ] start -->
        <div class="modal fade" id="missionDetailsModal" tabindex="-1" role="dialog" aria-labelledby="missionDetailsModalTitle" aria-hidden="true">
            <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="missionDetailsModalTitle">Détails de l'Atelier</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body" id="missionDetailsModalBody">
                        <!-- Le contenu sera chargé dynamiquement -->
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">Chargement...</span>
                            </div>
                            <p class="mt-3">Chargement des détails de l'atelier...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- [ Modal pour la gestion des étapes ] end -->

        <!-- [ Main Content ] end -->
    </div>
    <div id="alert-container" class="container-fluid position-fixed" style="bottom: 20px; right: 20px; z-index: 9999; max-width: 400px;"></div>
</div>
@endsection

@push('scripts')
<script>
function openMissionDetails(missionId) {
    $('#missionDetailsModal').modal('show');
    
    // Charger les détails via AJAX
    $.get("{{ route('workshop.details', '') }}/" + missionId, function(data) {
        $('#missionDetailsModalBody').html(data);
    }).fail(function() {
        $('#missionDetailsModalBody').html('<div class="alert alert-danger">Erreur lors du chargement des détails</div>');
    });
}

function deletePhoto(photoId) {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cette photo ?')) {
        return;
    }

    $.ajax({
        url: "{{ route('workshop.deletePhoto') }}",
        method: 'POST',
        data: {
            photo_id: photoId,
            _token: "{{ csrf_token() }}"
        },
        success: function(response) {
            if (response.success) {
                $('#photo-' + photoId).remove();
                showAlert('success', response.message);
            } else {
                showAlert('danger', response.message);
            }
        },
        error: function(xhr) {
            showAlert('danger', 'Erreur: ' + xhr.responseJSON.message);
        }
    });
}

function showAlert(type, message) {
    const alert = $(`
        <div class="alert alert-${type} alert-dismissible fade show">
            ${message}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    `);
    
    $('#alert-container').append(alert);
    
    setTimeout(() => {
        alert.alert('close');
    }, 5000);
}
</script>
@endpush