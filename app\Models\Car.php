<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Car extends Model
{
    use HasFactory;

    protected $table = 'cars';
    protected $guarded = [];

    protected $fillable = [
        'client_id',
        'marque',
        'modele',
        'annee',
        'immatriculation',
        'vin',
        'couleur',
        'kilometrage',
        'carburant',
        'transmission',
        'notes',
        'active'
    ];

    protected $casts = [
        'annee' => 'integer',
        'kilometrage' => 'integer',
        'active' => 'boolean'
    ];

    /**
     * Relation avec le client
     */
    public function client()
    {
        return $this->belongsTo(Client::class, 'client_id');
    }

    /**
     * Relation avec les rendez-vous
     */
    public function rdvs()
    {
        return $this->hasMany(Rdv::class, 'car_id');
    }

    /**
     * Obtenir le nom complet de la voiture
     */
    public function getFullNameAttribute()
    {
        return $this->marque . ' ' . $this->modele . ' (' . $this->annee . ')';
    }

    /**
     * Obtenir les informations de base de la voiture
     */
    public function getBasicInfoAttribute()
    {
        return $this->marque . ' ' . $this->modele . ' - ' . $this->immatriculation;
    }

    /**
     * Scope pour les voitures actives
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope pour rechercher par marque, modèle ou immatriculation
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function($q) use ($search) {
            $q->where('marque', 'like', "%{$search}%")
              ->orWhere('modele', 'like', "%{$search}%")
              ->orWhere('immatriculation', 'like', "%{$search}%")
              ->orWhere('vin', 'like', "%{$search}%");
        });
    }
}
