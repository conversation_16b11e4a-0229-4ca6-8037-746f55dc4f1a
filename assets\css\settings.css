/* Styles pour la page des paramètres */

/* Styles généraux */
.settings-section {
    margin-bottom: 30px;
}

.settings-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e2e5e8;
}

/* Styles pour les onglets */
.nav-tabs .nav-link {
    color: #495057;
    font-weight: 500;
}

.nav-tabs .nav-link.active {
    color: #4680ff;
    font-weight: 600;
}

/* Styles pour les formulaires */
.form-group label {
    font-weight: 500;
}

.custom-switch .custom-control-label {
    font-weight: normal;
}

/* Styles pour les badges */
.badge-admin {
    background-color: #ff5252;
    color: #fff;
}

.badge-technicien {
    background-color: #4099ff;
    color: #fff;
}

/* Styles pour les boutons d'action */
.action-buttons {
    white-space: nowrap;
}

.action-buttons .btn {
    margin-right: 5px;
}

/* Styles pour l'aperçu du logo */
.logo-preview {
    max-width: 100%;
    max-height: 150px;
    border: 1px solid #e2e5e8;
    padding: 10px;
    border-radius: 5px;
}

/* Styles pour le sélecteur de couleur */
.colorpicker-input-addon {
    width: 40px;
}

.colorpicker-input-addon i {
    width: 20px;
    height: 20px;
    display: inline-block;
    border-radius: 50%;
}

/* Styles pour le mode sombre */
body.dark-mode {
    background-color: #222;
    color: #f8f9fa;
}

body.dark-mode .card {
    background-color: #333;
    border-color: #444;
}

body.dark-mode .card-header {
    background-color: #444;
    border-color: #555;
}

body.dark-mode .table {
    color: #f8f9fa;
}

body.dark-mode .table thead th {
    border-color: #444;
}

body.dark-mode .table td {
    border-color: #444;
}

body.dark-mode .form-control {
    background-color: #444;
    border-color: #555;
    color: #f8f9fa;
}

body.dark-mode .form-control:focus {
    background-color: #444;
    color: #f8f9fa;
}

body.dark-mode .custom-file-label {
    background-color: #444;
    border-color: #555;
    color: #f8f9fa;
}

body.dark-mode .modal-content {
    background-color: #333;
    border-color: #444;
}

body.dark-mode .modal-header,
body.dark-mode .modal-footer {
    border-color: #444;
}

body.dark-mode .close {
    color: #f8f9fa;
}

body.dark-mode .text-muted {
    color: #adb5bd !important;
}

body.dark-mode .nav-tabs {
    border-color: #444;
}

body.dark-mode .nav-tabs .nav-link {
    color: #adb5bd;
}

body.dark-mode .nav-tabs .nav-link.active {
    color: #4680ff;
    background-color: #333;
    border-color: #444 #444 #333;
}

/* Styles pour les avatars d'utilisateurs */
.avatar-circle {
    width: 40px;
    height: 40px;
    background-color: #4680ff;
    color: #fff;
    text-align: center;
    line-height: 40px;
    border-radius: 50%;
    font-size: 16px;
    font-weight: 600;
    display: inline-block;
    margin-right: 10px;
}

/* Styles pour les tableaux responsifs */
@media (max-width: 768px) {
    .table-responsive {
        overflow-x: auto;
    }
    
    .action-buttons .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}

/* Styles pour les alertes */
.alert {
    border-radius: 5px;
}

.alert-dismissible .close {
    padding: 0.5rem 0.75rem;
}

/* Styles pour les cartes d'aperçu */
.preview-card {
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.preview-card .card-header {
    padding: 0.75rem 1.25rem;
}

.preview-card .card-body {
    padding: 1.25rem;
}

/* Styles pour les interrupteurs */
.custom-switch .custom-control-label::before {
    width: 2rem;
    height: 1rem;
    border-radius: 0.5rem;
}

.custom-switch .custom-control-label::after {
    width: calc(1rem - 4px);
    height: calc(1rem - 4px);
    border-radius: calc(0.5rem - 2px);
}

.custom-switch .custom-control-input:checked ~ .custom-control-label::after {
    transform: translateX(1rem);
}

/* Styles pour les champs obligatoires */
.required-field::after {
    content: "*";
    color: #ff5252;
    margin-left: 4px;
}
