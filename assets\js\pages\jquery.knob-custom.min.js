"use strict";$(window).on("resize",function(){$(".dial").knob({draw:function(){if("tron"==this.$.data("skin")){this.cursorExt=.3;var i,t=this.arc(this.cv);return this.g.lineWidth=this.lineWidth,this.o.displayPrevious&&(i=this.arc(this.v),this.g.beginPath(),this.g.strokeStyle=this.pColor,this.g.arc(this.xy,this.xy,this.radius-this.lineWidth,i.s,i.e,i.d),this.g.stroke()),this.g.beginPath(),this.g.strokeStyle=this.o.fgColor,this.g.arc(this.xy,this.xy,this.radius-this.lineWidth,t.s,t.e,t.d),this.g.stroke(),this.g.lineWidth=2,this.g.beginPath(),this.g.strokeStyle=this.o.fgColor,this.g.arc(this.xy,this.xy,this.radius-this.lineWidth+1+2*this.lineWidth/3,0,2*Math.PI,!1),this.g.stroke(),!1}}})}),$(document).ready(function(){$(".dial").knob({draw:function(){if("tron"==this.$.data("skin")){this.cursorExt=.3;var i,t=this.arc(this.cv);return this.g.lineWidth=this.lineWidth,this.o.displayPrevious&&(i=this.arc(this.v),this.g.beginPath(),this.g.strokeStyle=this.pColor,this.g.arc(this.xy,this.xy,this.radius-this.lineWidth,i.s,i.e,i.d),this.g.stroke()),this.g.beginPath(),this.g.strokeStyle=this.o.fgColor,this.g.arc(this.xy,this.xy,this.radius-this.lineWidth,t.s,t.e,t.d),this.g.stroke(),this.g.lineWidth=2,this.g.beginPath(),this.g.strokeStyle=this.o.fgColor,this.g.arc(this.xy,this.xy,this.radius-this.lineWidth+1+2*this.lineWidth/3,0,2*Math.PI,!1),this.g.stroke(),!1}}})});