<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\Car;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ClientController extends Controller
{
    /**
     * Afficher la liste des clients
     */
    public function index(Request $request)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $search = $request->input('search');
        $type = $request->input('type', 'all');
        $status = $request->input('status', 'all');

        $query = Client::with(['cars']);

        // Appliquer les filtres
        if ($search) {
            $query->search($search);
        }

        if ($type !== 'all') {
            $query->where('type_client', $type);
        }

        if ($status !== 'all') {
            $active = $status === 'active';
            $query->where('active', $active);
        }

        $clients = $query->orderBy('created_at', 'desc')->paginate(20);

        // Statistiques
        $stats = [
            'total' => Client::count(),
            'active' => Client::where('active', true)->count(),
            'inactive' => Client::where('active', false)->count(),
            'with_cars' => Client::has('cars')->count()
        ];

        return view('clients.index', compact('clients', 'stats', 'search', 'type', 'status'));
    }

    /**
     * Afficher le formulaire de création d'un client
     */
    public function create()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        return view('clients.create');
    }

    /**
     * Enregistrer un nouveau client
     */
    public function store(Request $request)
    {
        $request->validate([
            'prenom' => 'required|string|max:100',
            'nom' => 'required|string|max:100',
            'email' => 'nullable|email|unique:clients,email',
            'telephone' => 'nullable|string|max:20',
            'adresse' => 'nullable|string|max:255',
            'ville' => 'nullable|string|max:100',
            'code_postal' => 'nullable|string|max:10',
            'date_naissance' => 'nullable|date',
            'type_client' => 'required|in:particulier,professionnel',
            'notes' => 'nullable|string'
        ]);

        $client = Client::create($request->all());

        return redirect()->route('clients.show', $client)
            ->with('success', 'Client créé avec succès');
    }

    /**
     * Afficher les détails d'un client
     */
    public function show(Client $client)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $client->load(['cars.rdvs' => function($query) {
            $query->latest()->limit(10);
        }]);

        // Statistiques du client
        $stats = [
            'total_rdv' => $client->rdvs()->count(),
            'rdv_en_cours' => $client->rdvs()->where('statut', 'en_cours')->count(),
            'rdv_termines' => $client->rdvs()->where('statut', 'termine')->count(),
            'total_cars' => $client->cars()->count()
        ];

        return view('clients.show', compact('client', 'stats'));
    }

    /**
     * Afficher le formulaire d'édition d'un client
     */
    public function edit(Client $client)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        return view('clients.edit', compact('client'));
    }

    /**
     * Mettre à jour un client
     */
    public function update(Request $request, Client $client)
    {
        $request->validate([
            'prenom' => 'required|string|max:100',
            'nom' => 'required|string|max:100',
            'email' => 'nullable|email|unique:clients,email,' . $client->id,
            'telephone' => 'nullable|string|max:20',
            'adresse' => 'nullable|string|max:255',
            'ville' => 'nullable|string|max:100',
            'code_postal' => 'nullable|string|max:10',
            'date_naissance' => 'nullable|date',
            'type_client' => 'required|in:particulier,professionnel',
            'notes' => 'nullable|string'
        ]);

        $client->update($request->all());

        return redirect()->route('clients.show', $client)
            ->with('success', 'Client mis à jour avec succès');
    }

    /**
     * Supprimer un client
     */
    public function destroy(Client $client)
    {
        // Vérifier s'il y a des RDV associés
        if ($client->rdvs()->count() > 0) {
            return redirect()->route('clients.index')
                ->with('error', 'Impossible de supprimer ce client car il a des rendez-vous associés');
        }

        $client->delete();

        return redirect()->route('clients.index')
            ->with('success', 'Client supprimé avec succès');
    }

    /**
     * Activer/Désactiver un client
     */
    public function toggleStatus(Client $client)
    {
        $client->update(['active' => !$client->active]);

        $status = $client->active ? 'activé' : 'désactivé';
        
        return redirect()->back()
            ->with('success', "Client {$status} avec succès");
    }

    /**
     * Recherche AJAX pour l'autocomplétion
     */
    public function search(Request $request)
    {
        $search = $request->input('q');
        
        $clients = Client::where(function($query) use ($search) {
            $query->where('prenom', 'like', "%{$search}%")
                  ->orWhere('nom', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('telephone', 'like', "%{$search}%");
        })
        ->active()
        ->limit(10)
        ->get(['id', 'prenom', 'nom', 'email', 'telephone']);

        return response()->json($clients);
    }
}
