<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Stage extends Model
{
    use HasFactory;

    protected $table = 'stages';
    protected $guarded = [];

    protected $fillable = [
        'name',
        'description',
        'order',
        'color',
        'icon',
        'is_active',
        'estimated_duration'
    ];

    protected $casts = [
        'order' => 'integer',
        'is_active' => 'boolean',
        'estimated_duration' => 'integer'
    ];

    /**
     * Relation avec les étapes de RDV
     */
    public function rdvStages()
    {
        return $this->hasMany(RdvStage::class, 'stage_id');
    }

    /**
     * Relation avec les photos d'étapes
     */
    public function stagePhotos()
    {
        return $this->hasMany(StagePhoto::class, 'stage_id');
    }

    /**
     * Scope pour les étapes actives
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope pour ordonner par ordre
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('order');
    }

    /**
     * Obtenir la durée estimée formatée
     */
    public function getFormattedDurationAttribute()
    {
        if (!$this->estimated_duration) {
            return 'Non définie';
        }

        $hours = floor($this->estimated_duration / 60);
        $minutes = $this->estimated_duration % 60;

        if ($hours > 0) {
            return $hours . 'h ' . $minutes . 'min';
        }

        return $minutes . 'min';
    }
}