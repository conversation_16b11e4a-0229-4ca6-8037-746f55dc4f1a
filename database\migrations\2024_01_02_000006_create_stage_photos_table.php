<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stage_photos', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('rdv_id');
            $table->unsignedInteger('stage_id');
            $table->string('file_path');
            $table->string('file_name');
            $table->string('mime_type', 50)->nullable();
            $table->integer('file_size')->nullable();
            $table->text('notes')->nullable();
            $table->unsignedInteger('uploaded_by');
            $table->timestamps();
            
            $table->foreign('rdv_id')->references('id')->on('rdv')->onDelete('cascade');
            $table->foreign('stage_id')->references('id')->on('stages');
            $table->foreign('uploaded_by')->references('id')->on('users');
            
            $table->index(['rdv_id', 'stage_id']);
            $table->index('uploaded_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stage_photos');
    }
};
