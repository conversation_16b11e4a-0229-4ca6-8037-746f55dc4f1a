// Script pour mettre à jour le statut d'un rendez-vous
document.addEventListener('DOMContentLoaded', function() {
    // Ajouter un écouteur d'événements pour les changements de statut dans le modal d'édition
    const appointmentForm = document.getElementById('appointmentForm');
    if (appointmentForm) {
        appointmentForm.addEventListener('submit', function(e) {
            // Ne pas interférer avec la soumission normale du formulaire
            // Le formulaire sera traité par le serveur comme d'habitude
        });
    }

    // Ajouter un écouteur d'événements pour les changements de statut dans le modal de détails
    document.addEventListener('click', function(e) {
        // Vérifier si l'élément cliqué est un bouton de changement de statut
        if (e.target && e.target.classList.contains('change-status-btn')) {
            e.preventDefault();
            const rdvId = e.target.getAttribute('data-rdv-id');
            const newStatus = e.target.getAttribute('data-status');

            if (rdvId && newStatus) {
                updateAppointmentStatus(rdvId, newStatus);
            }
        }
    });

    // Fonction pour mettre à jour le statut d'un rendez-vous via AJAX
    function updateAppointmentStatus(rdvId, status) {
        // Créer un objet FormData pour envoyer les données
        const formData = new FormData();
        formData.append('rdv_id', rdvId);
        formData.append('status', status);

        // Afficher un indicateur de chargement
        showAlert('Mise à jour du statut en cours...', 'info');

        // Envoyer la requête AJAX
        console.log('Envoi de la requête à update_rdv_status.php');
        fetch('update_rdv_status.php', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Erreur réseau: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');

                // Mettre à jour l'interface utilisateur
                updateUIAfterStatusChange(rdvId, status, data.current_stage_id);

                // Fermer le modal de détails si ouvert
                if (typeof $ !== 'undefined' && $('#viewAppointmentModal').hasClass('show')) {
                    $('#viewAppointmentModal').modal('hide');
                }

                // Recharger la page après un court délai pour refléter les changements
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showAlert('Erreur: ' + (data.error || 'Erreur inconnue'), 'danger');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            showAlert('Erreur lors de la mise à jour du statut: ' + error.message, 'danger');
        });
    }

    // Fonction pour mettre à jour l'interface utilisateur après un changement de statut
    function updateUIAfterStatusChange(rdvId, status, currentStageId) {
        // Mettre à jour les éléments de l'interface utilisateur si nécessaire
        // Cette fonction peut être étendue selon les besoins spécifiques

        // Mettre à jour le badge de statut dans la vue calendrier
        const eventBoxes = document.querySelectorAll(`.event-box[data-rdv-id="${rdvId}"]`);
        eventBoxes.forEach(box => {
            // Supprimer toutes les classes de statut
            box.classList.remove('en_attente', 'confirme', 'en_cours', 'termine', 'annule');
            // Ajouter la nouvelle classe de statut
            box.classList.add(status);
        });

        // Mettre à jour le badge de statut dans la vue liste
        const statusCells = document.querySelectorAll(`tr[data-rdv-id="${rdvId}"] .status-badge`);
        statusCells.forEach(cell => {
            // Supprimer toutes les classes de statut
            cell.classList.remove('status-en_attente', 'status-confirme', 'status-en_cours', 'status-termine', 'status-annule');
            // Ajouter la nouvelle classe de statut
            cell.classList.add(`status-${status}`);

            // Mettre à jour le texte du badge
            let statusText = '';
            switch (status) {
                case 'en_attente': statusText = 'En attente'; break;
                case 'confirme': statusText = 'Confirmé'; break;
                case 'en_cours': statusText = 'En cours'; break;
                case 'termine': statusText = 'Terminé'; break;
                case 'annule': statusText = 'Annulé'; break;
                default: statusText = 'Inconnu'; break;
            }
            cell.textContent = statusText;
        });
    }

    // Fonction pour afficher des alertes
    function showAlert(message, type = 'info') {
        // Vérifier si la fonction showAlert existe déjà dans le contexte global
        if (typeof window.showAlert === 'function') {
            window.showAlert(message, type);
            return;
        }

        // Créer l'élément d'alerte
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.setAttribute('role', 'alert');

        // Ajouter le message
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        `;

        // Ajouter l'alerte au conteneur
        const alertContainer = document.getElementById('alert-container');
        if (alertContainer) {
            alertContainer.appendChild(alertDiv);

            // Supprimer l'alerte après 5 secondes
            setTimeout(() => {
                alertDiv.classList.remove('show');
                setTimeout(() => {
                    alertContainer.removeChild(alertDiv);
                }, 300);
            }, 5000);
        } else {
            console.error('Conteneur d\'alertes non trouvé');
            alert(message); // Fallback si le conteneur n'est pas trouvé
        }
    }
});
