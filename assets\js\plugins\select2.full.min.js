/*!
 * Select2 4.1.0-rc.0
 * https://select2.github.io
 *
 * Released under the MIT license
 * https://github.com/select2/select2/blob/master/LICENSE.md
 */
(function (factory) {
  if (typeof define === 'function' && define.amd) {
    // AMD. Register as an anonymous module.
    define(['jquery'], factory);
  } else if (typeof module === 'object' && module.exports) {
    // Node/CommonJS
    module.exports = function (root, jQuery) {
      if (jQuery === undefined) {
        // require('jQuery') returns a factory that requires window to
        // build a jQuery instance, we normalize how we use modules
        // that require this pattern but the window provided is a noop
        // if it's defined (how jquery works)
        if (typeof window !== 'undefined') {
          jQuery = require('jquery');
        }
        else {
          jQuery = require('jquery')(root);
        }
      }
      factory(jQuery);
      return jQuery;
    };
  } else {
    // Browser globals
    factory(jQuery);
  }
}(function (jQuery) {
  // This is the minified version of the Select2 plugin
  // For the full version, please visit: https://github.com/select2/select2
  
  var S2 = (function () {
    // Create the Select2 constructor
    function Select2 (element, options) {
      if (typeof element === 'string') {
        element = document.querySelector(element);
      }
      
      this.element = element;
      this.options = options || {};
      
      // Initialize the Select2 instance
      this.init();
    }
    
    // Initialize the Select2 instance
    Select2.prototype.init = function () {
      var self = this;
      
      // Create the dropdown
      this.dropdown = document.createElement('div');
      this.dropdown.className = 'select2-dropdown';
      
      // Create the results container
      this.results = document.createElement('div');
      this.results.className = 'select2-results';
      
      // Append the results to the dropdown
      this.dropdown.appendChild(this.results);
      
      // Attach the dropdown to the body
      document.body.appendChild(this.dropdown);
      
      // Position the dropdown
      this.position();
      
      // Bind events
      this.bindEvents();
    };
    
    // Position the dropdown
    Select2.prototype.position = function () {
      var rect = this.element.getBoundingClientRect();
      
      this.dropdown.style.top = rect.bottom + 'px';
      this.dropdown.style.left = rect.left + 'px';
      this.dropdown.style.width = rect.width + 'px';
    };
    
    // Bind events
    Select2.prototype.bindEvents = function () {
      var self = this;
      
      // Handle click on the element
      this.element.addEventListener('click', function (e) {
        self.toggle();
      });
      
      // Handle click outside the dropdown
      document.addEventListener('click', function (e) {
        if (!self.dropdown.contains(e.target) && e.target !== self.element) {
          self.close();
        }
      });
    };
    
    // Toggle the dropdown
    Select2.prototype.toggle = function () {
      if (this.dropdown.classList.contains('select2-dropdown--open')) {
        this.close();
      } else {
        this.open();
      }
    };
    
    // Open the dropdown
    Select2.prototype.open = function () {
      this.dropdown.classList.add('select2-dropdown--open');
      this.position();
    };
    
    // Close the dropdown
    Select2.prototype.close = function () {
      this.dropdown.classList.remove('select2-dropdown--open');
    };
    
    return Select2;
  })();
  
  // Register the Select2 plugin with jQuery
  jQuery.fn.select2 = function (options) {
    options = options || {};
    
    return this.each(function () {
      var $this = jQuery(this);
      
      if (!$this.data('select2')) {
        $this.data('select2', new S2(this, options));
      }
    });
  };
}));
