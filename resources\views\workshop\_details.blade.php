<div class="row">
    <!-- [ Informations générales ] start -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle mr-2"></i>Informations Générales</h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>RDV #:</strong></td>
                        <td>{{ $rdv->id }}</td>
                    </tr>
                    <tr>
                        <td><strong>Client:</strong></td>
                        <td>
                            @if($rdv->client)
                                {{ $rdv->client->prenom }} {{ $rdv->client->nom }}
                            @else
                                <span class="text-muted">Non défini</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Téléphone:</strong></td>
                        <td>{{ $rdv->client ? ($rdv->client->telephone ?? 'Non renseigné') : 'Non renseigné' }}</td>
                    </tr>
                    <tr>
                        <td><strong>Email:</strong></td>
                        <td>{{ $rdv->client ? ($rdv->client->email ?? 'Non renseigné') : 'Non renseigné' }}</td>
                    </tr>
                    <tr>
                        <td><strong>Véhicule:</strong></td>
                        <td>
                            @if($rdv->car)
                                {{ $rdv->car->marque }} {{ $rdv->car->modele }} ({{ $rdv->car->annee }})
                            @else
                                <span class="text-muted">Non défini</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Immatriculation:</strong></td>
                        <td>
                            @if($rdv->car)
                                <span class="badge badge-secondary">{{ $rdv->car->immatriculation }}</span>
                            @else
                                <span class="text-muted">Non définie</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Date RDV:</strong></td>
                        <td>{{ $rdv->jour_rdv->format('d/m/Y') }} à {{ $rdv->heure_rdv->format('H:i') }}</td>
                    </tr>
                    <tr>
                        <td><strong>Type:</strong></td>
                        <td>{{ $rdv->type_rdv }}</td>
                    </tr>
                    <tr>
                        <td><strong>Statut:</strong></td>
                        <td>
                            @php
                                $statusColor = $statusDefinitions[$rdv->statut]['color'] ?? 'secondary';
                            @endphp
                            <span class="badge badge-{{ $statusColor }}">{{ $rdv->formatted_status }}</span>
                        </td>
                    </tr>
                    @if($rdv->priority)
                    <tr>
                        <td><strong>Priorité:</strong></td>
                        <td>
                            @php
                                $priorityColor = $rdv->priority >= 4 ? 'danger' : ($rdv->priority >= 3 ? 'warning' : 'success');
                            @endphp
                            <span class="badge badge-{{ $priorityColor }}">{{ $rdv->priority }}/5</span>
                        </td>
                    </tr>
                    @endif
                </table>
            </div>
        </div>
    </div>
    <!-- [ Informations générales ] end -->

    <!-- [ Étape actuelle ] start -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-tasks mr-2"></i>Étape Actuelle</h5>
            </div>
            <div class="card-body">
                @php
                    $currentStage = $rdv->currentStage;
                    $stageColor = $stageDefinitions[$rdv->current_stage_id]['color'] ?? 'secondary';
                @endphp
                <div class="text-center mb-3">
                    <span class="badge badge-{{ $stageColor }} p-3 f-16">
                        {{ $currentStage->name ?? 'Étape non définie' }}
                    </span>
                </div>

                @if($currentStage && $currentStage->description)
                    <p class="text-muted">{{ $currentStage->description }}</p>
                @endif

                @if($rdv->latestStageStatus)
                    <div class="mt-3">
                        <h6>Dernière mise à jour:</h6>
                        <p class="mb-1">
                            <strong>Statut:</strong> {{ $rdv->latestStageStatus->formatted_status }}
                        </p>
                        <p class="mb-1">
                            <strong>Date:</strong> {{ $rdv->latestStageStatus->created_at->format('d/m/Y H:i') }}
                        </p>
                        @if($rdv->latestStageStatus->user)
                            <p class="mb-1">
                                <strong>Par:</strong> {{ $rdv->latestStageStatus->user->prenom }} {{ $rdv->latestStageStatus->user->nom }}
                            </p>
                        @endif
                        @if($rdv->latestStageStatus->notes)
                            <p class="mb-0">
                                <strong>Notes:</strong> {{ $rdv->latestStageStatus->notes }}
                            </p>
                        @endif
                    </div>
                @endif
            </div>
        </div>
    </div>
    <!-- [ Étape actuelle ] end -->
</div>

<!-- [ Description du travail ] start -->
@if($rdv->description)
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-file-alt mr-2"></i>Description du Travail</h5>
            </div>
            <div class="card-body">
                <p>{{ $rdv->description }}</p>
            </div>
        </div>
    </div>
</div>
@endif
<!-- [ Description du travail ] end -->

<!-- [ Photos ] start -->
@if($rdv->stagePhotos->count() > 0)
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-camera mr-2"></i>Photos ({{ $rdv->stagePhotos->count() }})</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    @foreach($rdv->stagePhotos as $photo)
                        <div class="col-md-3 mb-3">
                            <div class="card">
                                <img src="{{ Storage::url($photo->file_path) }}"
                                     class="card-img-top"
                                     style="height: 200px; object-fit: cover;"
                                     alt="Photo {{ $photo->stage->name ?? 'Étape' }}">
                                <div class="card-body p-2">
                                    <small class="text-muted">
                                        {{ $photo->stage->name ?? 'Étape inconnue' }}
                                        <br>{{ $photo->created_at->format('d/m/Y H:i') }}
                                    </small>
                                    @if($photo->notes)
                                        <p class="small mt-1 mb-0">{{ $photo->notes }}</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>
@endif
<!-- [ Photos ] end -->

<!-- [ Évaluations de dommages ] start -->
@if($rdv->damageAssessments->count() > 0)
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-clipboard-check mr-2"></i>Évaluations de Dommages</h5>
            </div>
            <div class="card-body">
                @foreach($rdv->damageAssessments as $assessment)
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <h6>{{ $assessment->damage_type }}</h6>
                                    <p class="mb-2">{{ $assessment->damage_description }}</p>
                                    <p class="mb-1">
                                        <strong>Méthode de réparation:</strong> {{ $assessment->repair_method }}
                                    </p>
                                    <p class="mb-1">
                                        <strong>Sévérité:</strong>
                                        <span class="badge badge-{{ $assessment->severity_level === 'critical' ? 'danger' : ($assessment->severity_level === 'high' ? 'warning' : 'info') }}">
                                            {{ $assessment->formatted_severity }}
                                        </span>
                                    </p>
                                </div>
                                <div class="col-md-4 text-right">
                                    @if($assessment->estimated_cost)
                                        <h5 class="text-primary">{{ number_format($assessment->estimated_cost, 2) }} €</h5>
                                    @endif
                                    @if($assessment->estimated_time)
                                        <p class="mb-0">
                                            <i class="fas fa-clock mr-1"></i>{{ $assessment->formatted_time }}
                                        </p>
                                    @endif
                                    @if($assessment->assessment_date)
                                        <small class="text-muted">
                                            Évalué le {{ $assessment->assessment_date->format('d/m/Y') }}
                                        </small>
                                    @endif
                                </div>
                            </div>

                            <!-- Pièces requises -->
                            @if($assessment->requiredParts->count() > 0)
                                <hr>
                                <h6>Pièces requises:</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Pièce</th>
                                                <th>Quantité</th>
                                                <th>Prix unitaire</th>
                                                <th>Total</th>
                                                <th>Statut</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($assessment->requiredParts as $part)
                                                <tr>
                                                    <td>{{ $part->part_name }}</td>
                                                    <td>{{ $part->quantity }}</td>
                                                    <td>{{ number_format($part->unit_price, 2) }} €</td>
                                                    <td>{{ number_format($part->total_price, 2) }} €</td>
                                                    <td>
                                                        <span class="badge badge-{{ $part->status === 'received' ? 'success' : ($part->status === 'ordered' ? 'warning' : 'secondary') }}">
                                                            {{ $part->formatted_status }}
                                                        </span>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</div>
@endif
<!-- [ Évaluations de dommages ] end -->

<!-- [ Historique des étapes ] start -->
@if($rdv->rdvStages->count() > 0)
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-history mr-2"></i>Historique des Étapes</h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    @foreach($rdv->rdvStages as $stage)
                        <div class="timeline-item">
                            <div class="timeline-marker bg-{{ $stageDefinitions[$stage->stage_id]['color'] ?? 'secondary' }}"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">{{ $stage->stage->name ?? 'Étape inconnue' }}</h6>
                                <p class="mb-1">
                                    <span class="badge badge-{{ $stage->status === 'termine' ? 'success' : ($stage->status === 'en_cours' ? 'primary' : 'warning') }}">
                                        {{ $stage->formatted_status }}
                                    </span>
                                </p>
                                <p class="mb-1">
                                    <small class="text-muted">
                                        Début: {{ $stage->start_date->format('d/m/Y H:i') }}
                                        @if($stage->end_date)
                                            | Fin: {{ $stage->end_date->format('d/m/Y H:i') }}
                                        @endif
                                    </small>
                                </p>
                                @if($stage->user)
                                    <p class="mb-1">
                                        <small class="text-muted">
                                            Par: {{ $stage->user->prenom }} {{ $stage->user->nom }}
                                        </small>
                                    </p>
                                @endif
                                @if($stage->notes)
                                    <p class="mb-0">{{ $stage->notes }}</p>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>
@endif
<!-- [ Historique des étapes ] end -->

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border-left: 3px solid #dee2e6;
}
</style>
