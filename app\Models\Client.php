<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Client extends Model
{
    use HasFactory;

    protected $table = 'clients';
    protected $guarded = [];

    protected $fillable = [
        'prenom',
        'nom',
        'email',
        'telephone',
        'adresse',
        'ville',
        'code_postal',
        'date_naissance',
        'type_client',
        'notes',
        'active'
    ];

    protected $casts = [
        'date_naissance' => 'date',
        'active' => 'boolean'
    ];

    /**
     * Relation avec les voitures
     */
    public function cars()
    {
        return $this->hasMany(Car::class, 'client_id');
    }

    /**
     * Relation avec les rendez-vous
     */
    public function rdvs()
    {
        return $this->hasManyThrough(Rdv::class, Car::class, 'client_id', 'car_id');
    }

    /**
     * Obtenir le nom complet du client
     */
    public function getFullNameAttribute()
    {
        return $this->prenom . ' ' . $this->nom;
    }

    /**
     * Scope pour les clients actifs
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope pour rechercher par nom ou email
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function($q) use ($search) {
            $q->where('prenom', 'like', "%{$search}%")
              ->orWhere('nom', 'like', "%{$search}%")
              ->orWhere('email', 'like', "%{$search}%")
              ->orWhere('telephone', 'like', "%{$search}%");
        });
    }
}
