<?php $__env->startSection('content'); ?>
<div class="pcoded-main-container">
    <div class="pcoded-content">
        <!-- [ breadcrumb ] start -->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10">Mon Profil</h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="<?php echo e(url('/')); ?>"><i class="feather icon-home"></i></a></li>
                            <li class="breadcrumb-item"><a href="#!">Profil</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!-- [ breadcrumb ] end -->

        <!-- [ Main Content ] start -->
        <div class="row">
            <!-- [ Informations du profil ] start -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <?php if($user->avatar): ?>
                                <img src="<?php echo e(Storage::url($user->avatar)); ?>"
                                     class="rounded-circle"
                                     width="120" height="120"
                                     alt="Avatar">
                            <?php else: ?>
                                <div class="avatar-circle bg-primary text-white mx-auto" style="width: 120px; height: 120px; font-size: 48px;">
                                    <?php echo e(strtoupper(substr($user->prenom ?? 'U', 0, 1))); ?><?php echo e(strtoupper(substr($user->nom ?? 'S', 0, 1))); ?>

                                </div>
                            <?php endif; ?>
                        </div>
                        <h5><?php echo e($user->prenom); ?> <?php echo e($user->nom); ?></h5>
                        <p class="text-muted"><?php echo e(ucfirst($user->role)); ?></p>

                        <!-- Boutons de gestion de l'avatar -->
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#avatarModal">
                                <i class="fas fa-camera"></i> Changer
                            </button>
                            <?php if($user->avatar): ?>
                                <form method="POST" action="<?php echo e(route('profile.deleteAvatar')); ?>" class="d-inline">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-sm btn-outline-danger"
                                            onclick="return confirm('Supprimer la photo de profil ?')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            <?php endif; ?>
                        </div>

                        <hr>

                        <!-- Informations de base -->
                        <div class="text-left">
                            <p><i class="fas fa-envelope mr-2"></i><?php echo e($user->email); ?></p>
                            <?php if($user->telephone): ?>
                                <p><i class="fas fa-phone mr-2"></i><?php echo e($user->telephone); ?></p>
                            <?php endif; ?>
                            <p><i class="fas fa-calendar mr-2"></i>Membre depuis <?php echo e($stats['membre_depuis']->format('d/m/Y')); ?></p>
                            <?php if($stats['derniere_connexion']): ?>
                                <p><i class="fas fa-clock mr-2"></i>Dernière connexion: <?php echo e($stats['derniere_connexion']->format('d/m/Y H:i')); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- [ Statistiques ] start -->
                <?php if($user->role === 'technicien'): ?>
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-bar mr-2"></i>Mes Statistiques</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <h4 class="text-primary"><?php echo e($stats['rdv_assignes']); ?></h4>
                                <p class="text-muted mb-0">RDV Assignés</p>
                            </div>
                            <div class="col-6 mb-3">
                                <h4 class="text-success"><?php echo e($stats['rdv_termines']); ?></h4>
                                <p class="text-muted mb-0">RDV Terminés</p>
                            </div>
                            <div class="col-6 mb-3">
                                <h4 class="text-warning"><?php echo e($stats['rdv_en_cours']); ?></h4>
                                <p class="text-muted mb-0">En Cours</p>
                            </div>
                            <div class="col-6 mb-3">
                                <h4 class="text-info"><?php echo e(round($stats['temps_total'] / 60, 1)); ?>h</h4>
                                <p class="text-muted mb-0">Temps Total</p>
                            </div>
                        </div>

                        <?php if($stats['rdv_assignes'] > 0): ?>
                            <?php
                                $tauxReussite = round(($stats['rdv_termines'] / $stats['rdv_assignes']) * 100, 2);
                            ?>
                            <div class="mt-3">
                                <p class="mb-1">Taux de réussite</p>
                                <div class="progress">
                                    <div class="progress-bar bg-<?php echo e($tauxReussite >= 90 ? 'success' : ($tauxReussite >= 70 ? 'warning' : 'danger')); ?>"
                                         style="width: <?php echo e($tauxReussite); ?>%">
                                        <?php echo e($tauxReussite); ?>%
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
                <!-- [ Statistiques ] end -->
            </div>
            <!-- [ Informations du profil ] end -->

            <!-- [ Paramètres ] start -->
            <div class="col-md-8">
                <!-- [ Onglets ] start -->
                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" data-toggle="tab" href="#profile-tab" role="tab">
                                    <i class="fas fa-user mr-1"></i>Profil
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-toggle="tab" href="#security-tab" role="tab">
                                    <i class="fas fa-lock mr-1"></i>Sécurité
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-toggle="tab" href="#notifications-tab" role="tab">
                                    <i class="fas fa-bell mr-1"></i>Notifications
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-toggle="tab" href="#activity-tab" role="tab">
                                    <i class="fas fa-history mr-1"></i>Activité
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content">
                            <!-- [ Onglet Profil ] start -->
                            <div class="tab-pane fade show active" id="profile-tab" role="tabpanel">
                                <form method="POST" action="<?php echo e(route('profile.update')); ?>">
                                    <?php echo csrf_field(); ?>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="prenom">Prénom *</label>
                                                <input type="text" class="form-control" id="prenom" name="prenom"
                                                       value="<?php echo e(old('prenom', $user->prenom)); ?>" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="nom">Nom *</label>
                                                <input type="text" class="form-control" id="nom" name="nom"
                                                       value="<?php echo e(old('nom', $user->nom)); ?>" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="email">Email *</label>
                                                <input type="email" class="form-control" id="email" name="email"
                                                       value="<?php echo e(old('email', $user->email)); ?>" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="telephone">Téléphone</label>
                                                <input type="text" class="form-control" id="telephone" name="telephone"
                                                       value="<?php echo e(old('telephone', $user->telephone)); ?>">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="theme_preference">Thème</label>
                                        <select class="form-control" id="theme_preference" name="theme_preference">
                                            <option value="system" <?php echo e($user->theme_preference === 'system' ? 'selected' : ''); ?>>
                                                Système (automatique)
                                            </option>
                                            <option value="light" <?php echo e($user->theme_preference === 'light' ? 'selected' : ''); ?>>
                                                Clair
                                            </option>
                                            <option value="dark" <?php echo e($user->theme_preference === 'dark' ? 'selected' : ''); ?>>
                                                Sombre
                                            </option>
                                        </select>
                                    </div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save mr-1"></i>Sauvegarder
                                    </button>
                                </form>
                            </div>
                            <!-- [ Onglet Profil ] end -->

                            <!-- [ Onglet Sécurité ] start -->
                            <div class="tab-pane fade" id="security-tab" role="tabpanel">
                                <form method="POST" action="<?php echo e(route('profile.updatePassword')); ?>">
                                    <?php echo csrf_field(); ?>
                                    <div class="form-group">
                                        <label for="current_password">Mot de passe actuel *</label>
                                        <input type="password" class="form-control" id="current_password"
                                               name="current_password" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="password">Nouveau mot de passe *</label>
                                        <input type="password" class="form-control" id="password"
                                               name="password" required>
                                        <small class="form-text text-muted">
                                            Minimum 8 caractères
                                        </small>
                                    </div>
                                    <div class="form-group">
                                        <label for="password_confirmation">Confirmer le mot de passe *</label>
                                        <input type="password" class="form-control" id="password_confirmation"
                                               name="password_confirmation" required>
                                    </div>
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-key mr-1"></i>Changer le mot de passe
                                    </button>
                                </form>

                                <hr>

                                <h6>Données personnelles</h6>
                                <p class="text-muted">Vous pouvez exporter vos données personnelles conformément au RGPD.</p>
                                <a href="<?php echo e(route('profile.exportData')); ?>" class="btn btn-outline-info">
                                    <i class="fas fa-download mr-1"></i>Exporter mes données
                                </a>
                            </div>
                            <!-- [ Onglet Sécurité ] end -->

                            <!-- [ Onglet Notifications ] start -->
                            <div class="tab-pane fade" id="notifications-tab" role="tabpanel">
                                <form method="POST" action="<?php echo e(route('profile.updateNotifications')); ?>">
                                    <?php echo csrf_field(); ?>
                                    <?php
                                        $notifications = json_decode($user->permissions ?? '{}', true)['notifications'] ?? [];
                                    ?>

                                    <h6>Notifications par email</h6>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="email_rdv" name="email_rdv"
                                               <?php echo e(($notifications['email_rdv'] ?? false) ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="email_rdv">
                                            Nouveaux rendez-vous
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="email_stage" name="email_stage"
                                               <?php echo e(($notifications['email_stage'] ?? false) ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="email_stage">
                                            Changements d'étapes
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="email_rappel" name="email_rappel"
                                               <?php echo e(($notifications['email_rappel'] ?? false) ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="email_rappel">
                                            Rappels de rendez-vous
                                        </label>
                                    </div>

                                    <hr>

                                    <h6>Notifications SMS</h6>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="sms_rdv" name="sms_rdv"
                                               <?php echo e(($notifications['sms_rdv'] ?? false) ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="sms_rdv">
                                            Confirmations de rendez-vous
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="sms_urgent" name="sms_urgent"
                                               <?php echo e(($notifications['sms_urgent'] ?? false) ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="sms_urgent">
                                            Notifications urgentes
                                        </label>
                                    </div>

                                    <hr>

                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save mr-1"></i>Sauvegarder les préférences
                                    </button>
                                </form>
                            </div>
                            <!-- [ Onglet Notifications ] end -->

                            <!-- [ Onglet Activité ] start -->
                            <div class="tab-pane fade" id="activity-tab" role="tabpanel">
                                <div id="activity-list">
                                    <div class="text-center py-3">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="sr-only">Chargement...</span>
                                        </div>
                                        <p class="mt-2">Chargement de l'activité...</p>
                                    </div>
                                </div>
                            </div>
                            <!-- [ Onglet Activité ] end -->
                        </div>
                    </div>
                </div>
                <!-- [ Onglets ] end -->
            </div>
            <!-- [ Paramètres ] end -->
        </div>
        <!-- [ Main Content ] end -->
    </div>
</div>

<!-- [ Modal Avatar ] start -->
<div class="modal fade" id="avatarModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Changer la photo de profil</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form method="POST" action="<?php echo e(route('profile.updateAvatar')); ?>" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="avatar">Sélectionner une image</label>
                        <input type="file" class="form-control-file" id="avatar" name="avatar"
                               accept="image/jpeg,image/png,image/jpg" required>
                        <small class="form-text text-muted">
                            Formats acceptés: JPEG, PNG, JPG. Taille maximum: 2MB.
                        </small>
                    </div>
                    <div id="avatar-preview" class="text-center" style="display: none;">
                        <img id="preview-image" src="" class="rounded-circle" width="120" height="120" alt="Aperçu">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Sauvegarder</button>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- [ Modal Avatar ] end -->

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Prévisualisation de l'avatar
document.getElementById('avatar').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('preview-image').src = e.target.result;
            document.getElementById('avatar-preview').style.display = 'block';
        };
        reader.readAsDataURL(file);
    }
});

// Charger l'activité quand l'onglet est activé
$('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
    if (e.target.getAttribute('href') === '#activity-tab') {
        loadActivity();
    }
});

function loadActivity() {
    $.get('<?php echo e(route("profile.activity")); ?>', function(activities) {
        let html = '';

        if (activities.length === 0) {
            html = '<div class="text-center py-3"><p class="text-muted">Aucune activité récente</p></div>';
        } else {
            activities.forEach(function(activity) {
                html += `
                    <div class="d-flex align-items-start mb-3">
                        <div class="avatar-circle bg-${activity.color} text-white mr-3" style="width: 40px; height: 40px; font-size: 16px;">
                            <i class="${activity.icon}"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${activity.title}</h6>
                            <p class="text-muted mb-1">${activity.description}</p>
                            <small class="text-muted">${new Date(activity.date).toLocaleString('fr-FR')}</small>
                        </div>
                    </div>
                `;
            });
        }

        document.getElementById('activity-list').innerHTML = html;
    }).fail(function() {
        document.getElementById('activity-list').innerHTML =
            '<div class="alert alert-danger">Erreur lors du chargement de l\'activité</div>';
    });
}

// Style pour les avatars
const style = document.createElement('style');
style.textContent = `
    .avatar-circle {
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
    }
`;
document.head.appendChild(style);
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\autolaravel\resources\views/profile/index.blade.php ENDPATH**/ ?>