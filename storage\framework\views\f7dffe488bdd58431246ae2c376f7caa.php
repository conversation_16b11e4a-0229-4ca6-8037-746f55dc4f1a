<?php $__env->startSection('content'); ?>
<div class="pcoded-main-container">
    <div class="pcoded-content">
        <!-- [ breadcrumb ] start -->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10">Atelier - Gestion des Travaux</h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="<?php echo e(url('/')); ?>"><i class="feather icon-home"></i></a></li>
                            <li class="breadcrumb-item"><a href="#!">Atelier</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!-- [ breadcrumb ] end -->

        <!-- [ Main Content ] start -->
        <div class="row">
            <!-- [ Statistiques rapides ] start -->
            <div class="col-md-6 col-xl-3">
                <div class="card bg-c-blue order-card">
                    <div class="card-body">
                        <h6 class="text-white">Total Travaux</h6>
                        <h2 class="text-white"><?php echo e($missions->count()); ?></h2>
                        <p class="m-b-0">En cours d'atelier</p>
                        <i class="fas fa-tools f-right f-26 text-c-blue"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-xl-3">
                <div class="card bg-c-green order-card">
                    <div class="card-body">
                        <h6 class="text-white">Photos Avant</h6>
                        <h2 class="text-white"><?php echo e($missions->where('current_stage_id', 2)->count()); ?></h2>
                        <p class="m-b-0">En attente photos</p>
                        <i class="fas fa-camera f-right f-26 text-c-green"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-xl-3">
                <div class="card bg-c-yellow order-card">
                    <div class="card-body">
                        <h6 class="text-white">Réparation</h6>
                        <h2 class="text-white"><?php echo e($missions->whereBetween('current_stage_id', [3, 8])->count()); ?></h2>
                        <p class="m-b-0">En réparation</p>
                        <i class="fas fa-wrench f-right f-26 text-c-yellow"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-xl-3">
                <div class="card bg-c-red order-card">
                    <div class="card-body">
                        <h6 class="text-white">Livraison</h6>
                        <h2 class="text-white"><?php echo e($missions->where('current_stage_id', 10)->count()); ?></h2>
                        <p class="m-b-0">Prêt livraison</p>
                        <i class="fas fa-shipping-fast f-right f-26 text-c-red"></i>
                    </div>
                </div>
            </div>
            <!-- [ Statistiques rapides ] end -->

            <!-- [ Filtres ] start -->
            <div class="col-sm-12 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-filter mr-2"></i>Filtres</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="<?php echo e(route('workshop.index')); ?>">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <label for="stage">Étape</label>
                                    <select class="form-control" id="stage" name="stage">
                                        <option value="all" <?php echo e($filters['stage'] === 'all' ? 'selected' : ''); ?>>Toutes les étapes</option>
                                        <?php $__currentLoopData = $stages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($stage->id); ?>" <?php echo e($filters['stage'] == $stage->id ? 'selected' : ''); ?>>
                                                <?php echo e($stage->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="date">Date</label>
                                    <input type="date" class="form-control" id="date" name="date" value="<?php echo e($filters['date']); ?>">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="search">Recherche</label>
                                    <input type="text" class="form-control" id="search" name="search" 
                                           placeholder="Client, immatriculation..." value="<?php echo e($filters['search']); ?>">
                                </div>
                                <div class="col-md-2 mb-3 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary mr-2">
                                        <i class="fas fa-search"></i> Filtrer
                                    </button>
                                    <a href="<?php echo e(route('workshop.index')); ?>" class="btn btn-outline-secondary">
                                        <i class="fas fa-undo"></i>
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- [ Filtres ] end -->

            <!-- [ Liste des travaux ] start -->
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list mr-2"></i>Travaux en Atelier</h5>
                        <div class="card-header-right">
                            <button class="btn btn-sm btn-primary" onclick="location.reload()">
                                <i class="fas fa-sync-alt"></i> Actualiser
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if($missions->count() > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>RDV #</th>
                                            <th>Client</th>
                                            <th>Véhicule</th>
                                            <th>Date RDV</th>
                                            <th>Étape Actuelle</th>
                                            <th>Statut</th>
                                            <th>Priorité</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $missions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php
                                                $currentStage = $mission->currentStage;
                                                $stageColor = $stageDefinitions[$mission->current_stage_id]['color'] ?? 'secondary';
                                                $statusColor = $statusDefinitions[$mission->statut]['color'] ?? 'secondary';
                                                $priorityColor = $mission->priority >= 4 ? 'danger' : ($mission->priority >= 3 ? 'warning' : 'success');
                                            ?>
                                            <tr>
                                                <td><strong>#<?php echo e($mission->id); ?></strong></td>
                                                <td>
                                                    <div>
                                                        <strong><?php echo e($mission->client->prenom); ?> <?php echo e($mission->client->nom); ?></strong>
                                                        <?php if($mission->client->telephone): ?>
                                                            <br><small class="text-muted"><?php echo e($mission->client->telephone); ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div>
                                                        <strong><?php echo e($mission->car->marque); ?> <?php echo e($mission->car->modele); ?></strong>
                                                        <br><small class="text-muted"><?php echo e($mission->car->immatriculation); ?></small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div>
                                                        <?php echo e($mission->jour_rdv->format('d/m/Y')); ?>

                                                        <br><small class="text-muted"><?php echo e($mission->heure_rdv->format('H:i')); ?></small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge badge-<?php echo e($stageColor); ?>">
                                                        <?php echo e($currentStage->name ?? 'Non défini'); ?>

                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge badge-<?php echo e($statusColor); ?>">
                                                        <?php echo e($mission->formatted_status); ?>

                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge badge-<?php echo e($priorityColor); ?>">
                                                        <?php echo e($mission->priority); ?>/5
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <button type="button" class="btn btn-sm btn-primary" 
                                                                onclick="openMissionDetails(<?php echo e($mission->id); ?>)">
                                                            <i class="fas fa-eye"></i> Détails
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-success" 
                                                                onclick="openStageManager(<?php echo e($mission->id); ?>)">
                                                            <i class="fas fa-tasks"></i> Gérer
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-5">
                                <i class="fas fa-tools fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">Aucun travail en atelier</h5>
                                <p class="text-muted">Il n'y a actuellement aucun travail correspondant aux critères sélectionnés.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <!-- [ Liste des travaux ] end -->
        </div>
        <!-- [ Main Content ] end -->
    </div>
</div>

<!-- [ Modal Détails ] start -->
<div class="modal fade" id="missionDetailsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Détails du Travail</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="missionDetailsModalBody">
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">Chargement...</span>
                    </div>
                    <p class="mt-3">Chargement des détails...</p>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- [ Modal Détails ] end -->

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function openMissionDetails(missionId) {
    $('#missionDetailsModal').modal('show');
    
    // Charger les détails via AJAX
    $.get("<?php echo e(route('workshop.details', '')); ?>/" + missionId, function(data) {
        $('#missionDetailsModalBody').html(data);
    }).fail(function() {
        $('#missionDetailsModalBody').html('<div class="alert alert-danger">Erreur lors du chargement des détails</div>');
    });
}

function openStageManager(missionId) {
    // Rediriger vers la page de gestion des étapes
    window.location.href = "<?php echo e(route('workshop.manage', '')); ?>/" + missionId;
}

// Auto-refresh toutes les 30 secondes
setInterval(function() {
    if (!$('.modal').hasClass('show')) {
        location.reload();
    }
}, 30000);
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\autolaravel\resources\views/workshop/index.blade.php ENDPATH**/ ?>