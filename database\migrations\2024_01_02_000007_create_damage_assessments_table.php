<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('damage_assessments', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('rdv_id');
            $table->string('damage_type', 100);
            $table->text('damage_description');
            $table->enum('severity_level', ['low', 'medium', 'high', 'critical'])->default('medium');
            $table->text('repair_method')->nullable();
            $table->decimal('estimated_cost', 10, 2)->nullable();
            $table->integer('estimated_time')->nullable(); // en minutes
            $table->json('photos')->nullable();
            $table->text('notes')->nullable();
            $table->unsignedInteger('assessed_by');
            $table->datetime('assessment_date');
            $table->timestamps();
            
            $table->foreign('rdv_id')->references('id')->on('rdv')->onDelete('cascade');
            $table->foreign('assessed_by')->references('id')->on('users');
            
            $table->index('rdv_id');
            $table->index('severity_level');
            $table->index('assessment_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('damage_assessments');
    }
};
