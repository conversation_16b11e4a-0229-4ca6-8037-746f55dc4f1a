// Fonction générique pour télécharger des photos
function uploadPhotos(options) {
    const {
        inputId,
        formId,
        notesId,
        containerId,
        uploadUrl,
        stageId,
        isUploading,
        setIsUploading,
        alertMessage,
        successLabelText
    } = options;

    console.log(`Fonction uploadPhotos appelée pour ${inputId}`);

    // Vérifier si un téléchargement est déjà en cours
    if (isUploading) {
        console.log('Un téléchargement est déjà en cours, annulation de cette demande');
        return false;
    }

    const photoUpload = document.getElementById(inputId);

    // Vérifier si le champ contient des photos
    let hasPhotos = false;

    if (photoUpload && photoUpload.files && photoUpload.files.length > 0) {
        console.log(`Photos trouvées dans ${inputId}:`, photoUpload.files.length);
        hasPhotos = true;
    }

    if (!hasPhotos) {
        console.log('Aucune photo trouvée');
        showAlert('Veuillez sélectionner au moins une photo', 'warning');
        return false;
    }

    // Marquer le début du téléchargement
    setIsUploading(true);

    console.log('Photos trouvées, préparation de l\'envoi');

    // Essayer de trouver l'ID du rendez-vous de différentes manières
    let rdvIdInput = document.querySelector(`#${formId} input[name="rdv_id"]`);

    if (!rdvIdInput) {
        rdvIdInput = document.querySelector('input[name="rdv_id"]');
    }

    // Si toujours pas trouvé, essayer de l'extraire de l'URL
    let rdvId;
    if (!rdvIdInput) {
        console.warn('Élément input[name="rdv_id"] non trouvé, tentative d\'extraction depuis l\'URL');
        const urlParams = new URLSearchParams(window.location.search);
        const missionId = urlParams.get('id');
        if (missionId) {
            rdvId = missionId;
            console.log('ID extrait de l\'URL:', rdvId);
        } else {
            // Dernière tentative: extraire de currentMissionId (variable globale)
            if (typeof currentMissionId !== 'undefined' && currentMissionId) {
                rdvId = currentMissionId;
                console.log('ID extrait de currentMissionId:', rdvId);
            } else {
                console.error('Impossible de trouver l\'ID du rendez-vous');
                showAlert('Erreur: ID de rendez-vous non trouvé', 'danger');
                setIsUploading(false);
                return false;
            }
        }
    } else {
        rdvId = rdvIdInput.value;
        console.log('ID du rendez-vous trouvé dans le formulaire:', rdvId);
    }

    // Créer un objet FormData pour envoyer les fichiers
    const formData = new FormData();
    formData.append('rdv_id', rdvId);
    formData.append('stage_id', stageId);

    // Ajouter les photos
    for (let i = 0; i < photoUpload.files.length; i++) {
        formData.append('photos[]', photoUpload.files[i]);
    }

    // Ajouter les notes si elles existent
    const notesInput = document.getElementById(notesId);
    if (notesInput) {
        formData.append('notes', notesInput.value);
    }

    // Afficher un indicateur de chargement
    showAlert(alertMessage || 'Téléchargement des photos en cours...', 'info');

    // Afficher les données qui seront envoyées (pour le débogage)
    console.log('Envoi des données:');
    console.log('- rdv_id:', rdvId);
    console.log('- stage_id:', stageId);
    console.log('- Nombre de photos:', photoUpload.files.length);

    // Afficher le contenu de formData pour le débogage
    console.log('Contenu de formData:');
    for (let pair of formData.entries()) {
        console.log(pair[0] + ': ' + (pair[1] instanceof File ? pair[1].name : pair[1]));
    }

    // Envoyer les données via AJAX
    fetch(uploadUrl, {
        method: 'POST',
        body: formData
    })
    .then(response => {
        console.log('Réponse du serveur (status):', response.status);
        return response.text().then(text => {
            console.log('Réponse brute du serveur:', text);
            try {
                // Essayer de parser la réponse comme JSON
                return JSON.parse(text);
            } catch (e) {
                // Si ce n'est pas du JSON, afficher le texte brut
                console.error('Réponse non-JSON:', text);
                console.error('Erreur de parsing:', e);
                throw new Error('Réponse invalide du serveur: ' + text.substring(0, 100));
            }
        });
    })
    .then(data => {
        console.log('Données reçues:', data);
        if (data.success) {
            showAlert(data.message, 'success');

            // Au lieu de recharger la page, ajouter les nouvelles photos à la galerie
            if (data.photos && data.photos.length > 0) {
                const photoContainer = document.getElementById(containerId);
                if (photoContainer) {
                    // Supprimer le message "Aucune photo" s'il existe
                    if (photoContainer.textContent.includes('Aucune photo')) {
                        photoContainer.innerHTML = '';
                    }

                    // Ajouter chaque nouvelle photo à la galerie
                    data.photos.forEach(photo => {
                        const photoElement = document.createElement('div');
                        photoElement.className = 'col-md-4 col-sm-6 mb-4';
                        photoElement.setAttribute('data-photo-id', photo.id);
                        photoElement.innerHTML = `
                            <div class="card h-100 position-relative">
                                <button type="button" class="btn-delete-photo" onclick="removeExistingPhoto(${photo.id})" title="Supprimer cette photo">
                                    <i class="fas fa-times"></i>
                                </button>
                                <img src="${photo.file_path}" class="card-img-top" alt="${photo.file_name}">
                                <div class="card-body">
                                    <p class="card-text small text-muted">${photo.file_name}</p>
                                    <p class="card-text small">${photo.upload_date ? new Date(photo.upload_date).toLocaleString() : ''}</p>
                                </div>
                            </div>
                        `;
                        photoContainer.appendChild(photoElement);
                    });
                }
            }

            // Réinitialiser le formulaire
            if (photoUpload) {
                photoUpload.value = '';
                const fileLabel = document.querySelector('.custom-file-label');
                if (fileLabel) {
                    fileLabel.textContent = successLabelText || 'Choisir des fichiers';
                }
            }

            // Réinitialiser le drapeau de téléchargement
            setIsUploading(false);
        } else {
            showAlert('Erreur: ' + (data.error || 'Erreur inconnue'), 'danger');
            // Réinitialiser le drapeau de téléchargement en cas d'erreur
            setIsUploading(false);
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        showAlert('Erreur lors du téléchargement des photos: ' + error.message, 'danger');
        // Réinitialiser le drapeau de téléchargement en cas d'erreur
        setIsUploading(false);
    });

    return true;
}
