<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add missing columns to rdv_stages table
        if (Schema::hasTable('rdv_stages')) {
            Schema::table('rdv_stages', function (Blueprint $table) {
                if (!Schema::hasColumn('rdv_stages', 'estimated_duration')) {
                    $table->integer('estimated_duration')->nullable();
                }
                if (!Schema::hasColumn('rdv_stages', 'actual_duration')) {
                    $table->integer('actual_duration')->nullable();
                }
            });
        }

        // Add missing columns to users table
        if (Schema::hasTable('users')) {
            Schema::table('users', function (Blueprint $table) {
                if (!Schema::hasColumn('users', 'prenom')) {
                    $table->string('prenom', 100)->nullable();
                }
                if (!Schema::hasColumn('users', 'nom')) {
                    $table->string('nom', 100)->nullable();
                }
                if (!Schema::hasColumn('users', 'telephone')) {
                    $table->string('telephone', 20)->nullable();
                }
                if (!Schema::hasColumn('users', 'role')) {
                    $table->string('role', 50)->default('technicien');
                }
                if (!Schema::hasColumn('users', 'active')) {
                    $table->boolean('active')->default(true);
                }
                if (!Schema::hasColumn('users', 'permissions')) {
                    $table->json('permissions')->nullable();
                }
                if (!Schema::hasColumn('users', 'theme_preference')) {
                    $table->string('theme_preference', 20)->default('system');
                }
                if (!Schema::hasColumn('users', 'last_login')) {
                    $table->timestamp('last_login')->nullable();
                }
                if (!Schema::hasColumn('users', 'avatar')) {
                    $table->string('avatar')->nullable();
                }
            });
        }

        // Add missing columns to rdv table
        if (Schema::hasTable('rdv')) {
            Schema::table('rdv', function (Blueprint $table) {
                if (!Schema::hasColumn('rdv', 'priority')) {
                    $table->integer('priority')->default(3);
                }
                if (!Schema::hasColumn('rdv', 'estimated_duration')) {
                    $table->integer('estimated_duration')->nullable();
                }
                if (!Schema::hasColumn('rdv', 'actual_duration')) {
                    $table->integer('actual_duration')->nullable();
                }
                if (!Schema::hasColumn('rdv', 'cost_estimate')) {
                    $table->decimal('cost_estimate', 10, 2)->nullable();
                }
                if (!Schema::hasColumn('rdv', 'final_cost')) {
                    $table->decimal('final_cost', 10, 2)->nullable();
                }
                if (!Schema::hasColumn('rdv', 'notes')) {
                    $table->text('notes')->nullable();
                }
                if (!Schema::hasColumn('rdv', 'created_by')) {
                    $table->unsignedInteger('created_by')->nullable();
                }
                if (!Schema::hasColumn('rdv', 'assigned_to')) {
                    $table->unsignedInteger('assigned_to')->nullable();
                }
            });
        }

        // Add missing columns to stages table
        if (Schema::hasTable('stages')) {
            Schema::table('stages', function (Blueprint $table) {
                if (!Schema::hasColumn('stages', 'description')) {
                    $table->text('description')->nullable();
                }
                if (!Schema::hasColumn('stages', 'order')) {
                    $table->integer('order')->default(0);
                }
                if (!Schema::hasColumn('stages', 'color')) {
                    $table->string('color', 20)->default('primary');
                }
                if (!Schema::hasColumn('stages', 'icon')) {
                    $table->string('icon', 50)->nullable();
                }
                if (!Schema::hasColumn('stages', 'is_active')) {
                    $table->boolean('is_active')->default(true);
                }
                if (!Schema::hasColumn('stages', 'estimated_duration')) {
                    $table->integer('estimated_duration')->nullable();
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove added columns if needed
        if (Schema::hasTable('rdv_stages')) {
            Schema::table('rdv_stages', function (Blueprint $table) {
                $table->dropColumn(['estimated_duration', 'actual_duration']);
            });
        }

        if (Schema::hasTable('users')) {
            Schema::table('users', function (Blueprint $table) {
                $table->dropColumn(['prenom', 'nom', 'telephone', 'role', 'active', 'permissions', 'theme_preference', 'last_login', 'avatar']);
            });
        }

        if (Schema::hasTable('rdv')) {
            Schema::table('rdv', function (Blueprint $table) {
                $table->dropColumn(['priority', 'estimated_duration', 'actual_duration', 'cost_estimate', 'final_cost', 'notes', 'created_by', 'assigned_to']);
            });
        }

        if (Schema::hasTable('stages')) {
            Schema::table('stages', function (Blueprint $table) {
                $table->dropColumn(['description', 'order', 'color', 'icon', 'is_active', 'estimated_duration']);
            });
        }
    }
};
