<?php

return [
    /*
    |--------------------------------------------------------------------------
    | AutoFix Pro Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration spécifique pour l'application AutoFix Pro
    |
    */

    'app_name' => 'AutoFix Pro',
    'app_version' => '1.0.0',
    'app_description' => 'Système de gestion d\'atelier automobile',

    /*
    |--------------------------------------------------------------------------
    | Paramètres de l'atelier
    |--------------------------------------------------------------------------
    */
    'workshop' => [
        'opening_time' => '08:00',
        'closing_time' => '18:00',
        'lunch_break_start' => '12:00',
        'lunch_break_end' => '13:00',
        'slot_duration' => 30, // minutes
        'max_concurrent_jobs' => 10,
        'default_stage_id' => 1,
    ],

    /*
    |--------------------------------------------------------------------------
    | Paramètres des rendez-vous
    |--------------------------------------------------------------------------
    */
    'appointments' => [
        'default_duration' => 60, // minutes
        'max_advance_booking' => 90, // jours
        'reminder_hours' => [24, 2], // heures avant le RDV
        'auto_confirm' => false,
        'allow_weekend' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | Paramètres des photos
    |--------------------------------------------------------------------------
    */
    'photos' => [
        'max_size' => 5120, // KB
        'allowed_types' => ['jpg', 'jpeg', 'png', 'webp'],
        'max_per_stage' => 10,
        'storage_disk' => 'public',
        'storage_path' => 'photos',
        'thumbnail_size' => [300, 300],
    ],

    /*
    |--------------------------------------------------------------------------
    | Paramètres des notifications
    |--------------------------------------------------------------------------
    */
    'notifications' => [
        'email' => [
            'enabled' => true,
            'from_address' => env('MAIL_FROM_ADDRESS', '<EMAIL>'),
            'from_name' => env('MAIL_FROM_NAME', 'AutoFix Pro'),
        ],
        'sms' => [
            'enabled' => false,
            'provider' => 'twilio', // twilio, nexmo, etc.
            'from_number' => env('SMS_FROM_NUMBER'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Paramètres des rôles et permissions
    |--------------------------------------------------------------------------
    */
    'roles' => [
        'admin' => [
            'name' => 'Administrateur',
            'permissions' => ['all'],
        ],
        'manager' => [
            'name' => 'Responsable',
            'permissions' => ['workshop', 'clients', 'reports', 'kpi'],
        ],
        'technicien' => [
            'name' => 'Technicien',
            'permissions' => ['workshop', 'photos', 'stages'],
        ],
        'receptionist' => [
            'name' => 'Réceptionniste',
            'permissions' => ['clients', 'appointments', 'calendar'],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Paramètres des étapes
    |--------------------------------------------------------------------------
    */
    'stages' => [
        'colors' => [
            'secondary' => '#6c757d',
            'info' => '#17a2b8',
            'warning' => '#ffc107',
            'primary' => '#007bff',
            'danger' => '#dc3545',
            'success' => '#28a745',
        ],
        'auto_progress' => false,
        'require_photos' => [2, 8], // IDs des étapes nécessitant des photos
        'require_approval' => [6, 9], // IDs des étapes nécessitant une approbation
    ],

    /*
    |--------------------------------------------------------------------------
    | Paramètres financiers
    |--------------------------------------------------------------------------
    */
    'financial' => [
        'currency' => 'EUR',
        'currency_symbol' => '€',
        'tax_rate' => 20.0, // %
        'payment_methods' => ['cash', 'card', 'transfer', 'check'],
        'invoice_prefix' => 'AF',
        'invoice_start_number' => 1000,
    ],

    /*
    |--------------------------------------------------------------------------
    | Paramètres des rapports
    |--------------------------------------------------------------------------
    */
    'reports' => [
        'default_period' => 'month',
        'export_formats' => ['pdf', 'excel', 'csv'],
        'cache_duration' => 3600, // secondes
        'max_export_records' => 10000,
    ],

    /*
    |--------------------------------------------------------------------------
    | Paramètres de sécurité
    |--------------------------------------------------------------------------
    */
    'security' => [
        'session_timeout' => 480, // minutes
        'max_login_attempts' => 5,
        'lockout_duration' => 15, // minutes
        'password_expiry' => 90, // jours
        'require_2fa' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | Paramètres de l'interface
    |--------------------------------------------------------------------------
    */
    'ui' => [
        'default_theme' => 'light',
        'available_themes' => ['light', 'dark', 'system'],
        'default_language' => 'fr',
        'available_languages' => ['fr', 'en'],
        'items_per_page' => 20,
        'date_format' => 'd/m/Y',
        'time_format' => 'H:i',
        'datetime_format' => 'd/m/Y H:i',
    ],

    /*
    |--------------------------------------------------------------------------
    | Paramètres de maintenance
    |--------------------------------------------------------------------------
    */
    'maintenance' => [
        'backup_frequency' => 'daily',
        'backup_retention' => 30, // jours
        'log_retention' => 90, // jours
        'cleanup_temp_files' => true,
        'optimize_images' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Intégrations externes
    |--------------------------------------------------------------------------
    */
    'integrations' => [
        'google_maps' => [
            'enabled' => false,
            'api_key' => env('GOOGLE_MAPS_API_KEY'),
        ],
        'parts_suppliers' => [
            'enabled' => false,
            'default_supplier' => 'autodoc',
        ],
        'accounting' => [
            'enabled' => false,
            'software' => 'sage', // sage, quickbooks, etc.
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Paramètres de développement
    |--------------------------------------------------------------------------
    */
    'development' => [
        'debug_toolbar' => env('APP_DEBUG', false),
        'log_queries' => env('LOG_QUERIES', false),
        'fake_notifications' => env('FAKE_NOTIFICATIONS', false),
        'demo_mode' => env('DEMO_MODE', false),
    ],
];
