<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Exception;

class StatsController extends Controller
{
    public function getRdvStats()
    {
        try {
            $result = [
                'success' => true,
                'labels' => ["En attente", "En cours", "Terminé"],
                'colors' => ["#3b82f6", "#65a30d", "#14b8a6"],
                'weeks' => [],
            ];

            $currentDate = Carbon::now();
            $weeksToCheckPast = 8;
            $weeksToCheckFuture = 8;

            $weekStats = [];

            // Semaine courante
            $thisWeekStart = $currentDate->copy()->startOfWeek();
            $thisWeekEnd = $thisWeekStart->copy()->endOfWeek();

            $weekStats[] = $this->getWeekStat($thisWeekStart, $thisWeekEnd, true);

            // Semaines passées
            $weekStart = $thisWeekStart->copy();
            for ($i = 1; $i <= $weeksToCheckPast; $i++) {
                $weekStart->subWeek();
                $weekEnd = $weekStart->copy()->endOfWeek();
                $weekStats[] = $this->getWeekStat($weekStart, $weekEnd, false, true);
            }

            // Semaines futures
            $weekStart = $thisWeekStart->copy();
            for ($i = 1; $i <= $weeksToCheckFuture; $i++) {
                $weekStart->addWeek();
                $weekEnd = $weekStart->copy()->endOfWeek();
                $weekStats[] = $this->getWeekStat($weekStart, $weekEnd, false, false, true);
            }

            // Ajouter seulement les semaines avec des données
            foreach ($weekStats as $week) {
                if (array_sum($week['values']) > 0) {
                    $result['weeks'][] = [
                        'from' => $week['from'],
                        'to' => $week['to'],
                        'values' => $week['values']
                    ];
                }
            }

            // Si aucune donnée, on ajoute la semaine courante vide
            if (empty($result['weeks'])) {
                $result['weeks'][] = [
                    'from' => $thisWeekStart->format('d M'),
                    'to' => $thisWeekEnd->format('d M'),
                    'values' => [0, 0, 0]
                ];
            }

            // Limiter à 4 semaines pour l'affichage
            $result['weeks'] = array_slice($result['weeks'], 0, 4);

            // Calculer les totaux
            $series = [0, 0, 0];
            foreach ($result['weeks'] as $week) {
                for ($i = 0; $i < 3; $i++) {
                    $series[$i] += $week['values'][$i];
                }
            }

            $result['series'] = $series;
            $result['total'] = array_sum($series);

            return response()->json($result);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Erreur de base de données: ' . $e->getMessage()
            ], 500);
        }
    }

    private function getWeekStat($start, $end, $isCurrent = false, $isPast = false, $isFuture = false)
    {
        $stats = DB::table('rdv')
            ->selectRaw("
                SUM(CASE WHEN statut = 'en_attente' THEN 1 ELSE 0 END) as en_attente,
                SUM(CASE WHEN statut = 'en_cours' THEN 1 ELSE 0 END) as en_cours,
                SUM(CASE WHEN statut = 'termine' THEN 1 ELSE 0 END) as termine
            ")
            ->whereBetween('jour_rdv', [$start->format('Y-m-d'), $end->format('Y-m-d')])
            ->first();

        return [
            'from' => $start->format('d M'),
            'to' => $end->format('d M'),
            'values' => [
                (int)$stats->en_attente,
                (int)$stats->en_cours,
                (int)$stats->termine
            ],
            'is_current' => $isCurrent,
            'is_past' => $isPast,
            'is_future' => $isFuture
        ];
    }

    /**
     * Get dashboard overview statistics
     */
    public function getDashboardStats()
    {
        try {
            $today = Carbon::today();

            $stats = [
                'success' => true,
                'total_rdv' => DB::table('rdv')->count(),
                'rdv_today' => DB::table('rdv')->whereDate('jour_rdv', $today)->count(),
                'active_missions' => DB::table('rdv')
                    ->whereIn('statut', ['confirme', 'en_cours'])
                    ->where('current_stage_id', '>', 1)
                    ->count(),
                'total_clients' => DB::table('clients')->where('active', true)->count(),
                'rdv_this_week' => DB::table('rdv')
                    ->whereBetween('jour_rdv', [
                        $today->copy()->startOfWeek()->format('Y-m-d'),
                        $today->copy()->endOfWeek()->format('Y-m-d')
                    ])
                    ->count(),
                'completed_this_month' => DB::table('rdv')
                    ->where('statut', 'termine')
                    ->whereMonth('jour_rdv', $today->month)
                    ->whereYear('jour_rdv', $today->year)
                    ->count()
            ];

            return response()->json($stats);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Erreur lors du chargement des statistiques: ' . $e->getMessage()
            ], 500);
        }
    }
}
