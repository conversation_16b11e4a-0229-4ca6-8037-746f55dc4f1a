<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
             Schema::create('users', function (Blueprint $table) {
        $table->increments('id');
        $table->string('prenom', 100);
        $table->string('nom', 100)->nullable();
        $table->string('email', 100)->unique()->nullable();
        $table->string('telephone', 20)->nullable();
        $table->string('password', 255)->nullable();
        $table->enum('role', ['admin', 'technicien'])->default('technicien');
        $table->boolean('active')->default(1);
        $table->text('permissions')->nullable();
        $table->string('theme_preference', 20)->default('system');
        $table->dateTime('last_login')->nullable();
        $table->rememberToken();
        $table->timestamps();
        });

        Schema::create('password_reset_tokens', function (Blueprint $table) {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });

        Schema::create('sessions', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->foreignId('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('sessions');
    }
};
