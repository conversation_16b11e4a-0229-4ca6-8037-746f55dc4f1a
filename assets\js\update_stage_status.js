// Script pour mettre à jour le statut d'une étape de rendez-vous
document.addEventListener('DOMContentLoaded', function() {
    // Ajouter un écouteur d'événements pour les boutons de complétion d'étape
    document.addEventListener('click', function(e) {
        // Vérifier si l'élément cliqué est un bouton de complétion d'étape
        if (e.target && e.target.classList.contains('complete-stage-btn')) {
            e.preventDefault();
            const rdvId = e.target.getAttribute('data-rdv-id');
            const stageId = e.target.getAttribute('data-stage-id');
            const status = e.target.getAttribute('data-status') || 'termine';
            const notes = document.getElementById('stage_notes_' + stageId)?.value || '';
            
            if (rdvId && stageId) {
                updateStageStatus(rdvId, stageId, status, notes);
            }
        }
    });
    
    // Fonction pour mettre à jour le statut d'une étape via AJAX
    function updateStageStatus(rdvId, stageId, status, notes) {
        // Créer un objet FormData pour envoyer les données
        const formData = new FormData();
        formData.append('rdv_id', rdvId);
        formData.append('stage_id', stageId);
        formData.append('status', status);
        formData.append('notes', notes);
        
        // Afficher un indicateur de chargement
        showAlert('Mise à jour du statut de l\'étape en cours...', 'info');
        
        // Envoyer la requête AJAX
        console.log('Envoi de la requête à update_stage_status.php');
        fetch('update_stage_status.php', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Erreur réseau: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                
                // Si le rendez-vous est marqué comme terminé, mettre à jour l'interface
                if (data.rdv_status === 'termine') {
                    // Mettre à jour le badge de statut
                    const statusBadges = document.querySelectorAll('.status-badge');
                    statusBadges.forEach(badge => {
                        badge.textContent = 'Terminé';
                        badge.className = 'badge badge-success status-badge status-termine';
                    });
                }
                
                // Recharger la page après un court délai pour refléter les changements
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showAlert('Erreur: ' + (data.error || 'Erreur inconnue'), 'danger');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            showAlert('Erreur lors de la mise à jour du statut de l\'étape: ' + error.message, 'danger');
        });
    }
    
    // Fonction pour afficher des alertes
    function showAlert(message, type = 'info') {
        // Vérifier si la fonction showAlert existe déjà dans le contexte global
        if (typeof window.showAlert === 'function') {
            window.showAlert(message, type);
            return;
        }
        
        // Créer l'élément d'alerte
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.setAttribute('role', 'alert');
        
        // Ajouter le message
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        `;
        
        // Ajouter l'alerte au conteneur
        const alertContainer = document.getElementById('alert-container');
        if (alertContainer) {
            alertContainer.appendChild(alertDiv);
            
            // Supprimer l'alerte après 5 secondes
            setTimeout(() => {
                alertDiv.classList.remove('show');
                setTimeout(() => {
                    alertContainer.removeChild(alertDiv);
                }, 300);
            }, 5000);
        } else {
            console.error('Conteneur d\'alertes non trouvé');
            alert(message); // Fallback si le conteneur n'est pas trouvé
        }
    }
});
