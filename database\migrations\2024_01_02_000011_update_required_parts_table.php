<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('required_parts')) {
            Schema::table('required_parts', function (Blueprint $table) {
                // Add missing columns
                if (!Schema::hasColumn('required_parts', 'damage_assessment_id')) {
                    $table->unsignedInteger('damage_assessment_id')->nullable()->after('id');
                }
                
                if (!Schema::hasColumn('required_parts', 'unit_price')) {
                    $table->decimal('unit_price', 10, 2)->nullable()->after('quantity');
                }
                
                if (!Schema::hasColumn('required_parts', 'total_price')) {
                    $table->decimal('total_price', 10, 2)->nullable()->after('unit_price');
                }
                
                if (!Schema::hasColumn('required_parts', 'supplier')) {
                    $table->string('supplier', 100)->nullable()->after('total_price');
                }
                
                if (!Schema::hasColumn('required_parts', 'ordered_date')) {
                    $table->date('ordered_date')->nullable()->after('supplier');
                }
                
                if (!Schema::hasColumn('required_parts', 'received_date')) {
                    $table->date('received_date')->nullable()->after('ordered_date');
                }
                
                // Update status enum to match our model
                if (Schema::hasColumn('required_parts', 'status')) {
                    $table->enum('status', ['needed', 'ordered', 'received', 'installed', 'cancelled'])
                          ->default('needed')->change();
                }
                
                // Add timestamps if they don't exist
                if (!Schema::hasColumn('required_parts', 'created_at')) {
                    $table->timestamps();
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('required_parts')) {
            Schema::table('required_parts', function (Blueprint $table) {
                $table->dropColumn([
                    'damage_assessment_id', 
                    'unit_price', 
                    'total_price', 
                    'supplier', 
                    'ordered_date', 
                    'received_date'
                ]);
            });
        }
    }
};
