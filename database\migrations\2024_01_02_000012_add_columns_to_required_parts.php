<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('required_parts')) {
            Schema::table('required_parts', function (Blueprint $table) {
                // Add missing columns only
                if (!Schema::hasColumn('required_parts', 'damage_assessment_id')) {
                    $table->unsignedInteger('damage_assessment_id')->nullable();
                }
                
                if (!Schema::hasColumn('required_parts', 'unit_price')) {
                    $table->decimal('unit_price', 10, 2)->nullable();
                }
                
                if (!Schema::hasColumn('required_parts', 'total_price')) {
                    $table->decimal('total_price', 10, 2)->nullable();
                }
                
                if (!Schema::hasColumn('required_parts', 'supplier')) {
                    $table->string('supplier', 100)->nullable();
                }
                
                if (!Schema::hasColumn('required_parts', 'ordered_date')) {
                    $table->date('ordered_date')->nullable();
                }
                
                if (!Schema::hasColumn('required_parts', 'received_date')) {
                    $table->date('received_date')->nullable();
                }
                
                // Add timestamps if they don't exist
                if (!Schema::hasColumn('required_parts', 'created_at')) {
                    $table->timestamps();
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('required_parts')) {
            Schema::table('required_parts', function (Blueprint $table) {
                $table->dropColumn([
                    'damage_assessment_id', 
                    'unit_price', 
                    'total_price', 
                    'supplier', 
                    'ordered_date', 
                    'received_date',
                    'created_at',
                    'updated_at'
                ]);
            });
        }
    }
};
