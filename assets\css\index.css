
    .container {
      display: flex;
      align-items: flex-start;
      gap: 40px;
    }

    .left-section {
      text-align: center;
    }

    .left-section h3 {
      margin-bottom: 5px;
    }

    .right-section {
      flex-grow: 1;
    }

    .week-block {
      display: flex;
      align-items: center;
      margin-bottom: 24px;
    }

    .calendar {
      width: 70px;
      height: 80px;
      background: #f3f3f3;
      border-radius: 12px;
      padding: 8px 6px;
      margin-right: 15px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      text-align: center;
      position: relative;
      font-size: 13px;
    }

    .calendar-icon {
      width: 100%;
      height: 18px;
      background-color: #f87171; /* red */
      border-radius: 6px 6px 0 0;
      position: absolute;
      top: 0;
      left: 0;
    }

    .calendar-content {
      margin-top: 20px;
    }

    .calendar-date span {
      display: block;
      font-weight: bold;
      line-height: 1.2;
    }

    .calendar-date span:first-child {
      color: #111;
      font-size: 13px;
    }

    .calendar-date span:last-child {
      color: #555;
      font-size: 12px;
    }

    .bar-block {
      flex-grow: 1;
    }

    .bar-title {
      font-weight: bold;
      color: #666;
      margin-bottom: 6px;
    }

    .bar {
      display: flex;
      height: 10px;
      border-radius: 5px;
      overflow: hidden;
    }

    .bar > div {
      height: 100%;
    }

    .blue { background-color: #3b82f6; }
    .green { background-color: #65a30d; }
    .cyan { background-color: #14b8a6; }

    #donut-chart {
      max-width: 260px;
      margin: auto;
    }

    .donut-title {
      font-size: 16px;
      color: #777;
      margin-top: 10px;
    }
    .custom-total {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 24px;
      font-weight: 800;
      color: #2c3e50;
      pointer-events: none;
      z-index: 10;
    }

    .bar-item {
        margin-bottom: 14px;
    }

    .bar-label {
        color: grey;
    }

    .bar-line {
        height: 10px;
        border-radius: 10px;
        margin-top: 4px;
    }

    .apexcharts-datalabel-label,
    .apexcharts-datalabel-value,
    .apexcharts-datalabel-total {
        font-family: 'Arial', sans-serif !important;
        text-anchor: middle !important;
    }
