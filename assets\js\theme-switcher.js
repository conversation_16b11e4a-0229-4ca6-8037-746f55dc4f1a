/**
 * Script pour gérer le changement de thème (clair/sombre)
 */
document.addEventListener('DOMContentLoaded', function() {
    // Fonction pour appliquer le thème
    function applyTheme(theme) {
        if (theme === 'dark') {
            document.body.classList.add('dark-mode');
            localStorage.setItem('theme', 'dark');
        } else {
            document.body.classList.remove('dark-mode');
            localStorage.setItem('theme', 'light');
        }
    }
    
    // Récupérer le thème enregistré dans le localStorage
    const savedTheme = localStorage.getItem('theme');
    
    // Appliquer le thème enregistré ou le thème par défaut
    if (savedTheme) {
        applyTheme(savedTheme);
        
        // Mettre à jour le sélecteur de thème si on est sur la page des paramètres
        const themeSelector = document.getElementById('setting_theme_mode');
        if (themeSelector) {
            themeSelector.value = savedTheme;
        }
    }
    
    // Écouter les changements du sélecteur de thème
    const themeSelector = document.getElementById('setting_theme_mode');
    if (themeSelector) {
        themeSelector.addEventListener('change', function() {
            applyTheme(this.value);
        });
    }
    
    // Ajouter un bouton de basculement rapide dans la barre de navigation
    const userMenu = document.getElementById('nav-user-link');
    if (userMenu) {
        const themeToggleItem = document.createElement('li');
        themeToggleItem.className = 'list-group-item';
        
        const themeToggleLink = document.createElement('a');
        themeToggleLink.href = '#';
        themeToggleLink.id = 'theme-toggle';
        
        const icon = document.createElement('i');
        icon.className = savedTheme === 'dark' ? 'feather icon-sun m-r-5' : 'feather icon-moon m-r-5';
        
        themeToggleLink.appendChild(icon);
        themeToggleLink.appendChild(document.createTextNode(savedTheme === 'dark' ? 'Mode clair' : 'Mode sombre'));
        
        themeToggleItem.appendChild(themeToggleLink);
        userMenu.insertBefore(themeToggleItem, userMenu.firstChild);
        
        // Ajouter l'événement de clic pour basculer le thème
        themeToggleLink.addEventListener('click', function(e) {
            e.preventDefault();
            const currentTheme = localStorage.getItem('theme') || 'light';
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            
            applyTheme(newTheme);
            
            // Mettre à jour l'icône et le texte
            icon.className = newTheme === 'dark' ? 'feather icon-sun m-r-5' : 'feather icon-moon m-r-5';
            themeToggleLink.textContent = '';
            themeToggleLink.appendChild(icon);
            themeToggleLink.appendChild(document.createTextNode(newTheme === 'dark' ? 'Mode clair' : 'Mode sombre'));
            
            // Mettre à jour le sélecteur de thème si on est sur la page des paramètres
            const themeSelector = document.getElementById('setting_theme_mode');
            if (themeSelector) {
                themeSelector.value = newTheme;
            }
        });
    }
});
