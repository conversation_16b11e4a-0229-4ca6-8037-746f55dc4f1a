<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UpdateStagesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $stages = [
            [
                'id' => 1,
                'name' => 'Demande à traiter',
                'description' => 'Nouvelle demande de rendez-vous en attente de traitement',
                'order' => 1,
                'color' => 'secondary',
                'icon' => 'fas fa-inbox',
                'is_active' => true,
                'estimated_duration' => 15
            ],
            [
                'id' => 2,
                'name' => 'Photos avant réparation',
                'description' => 'Prise de photos avant intervention',
                'order' => 2,
                'color' => 'info',
                'icon' => 'fas fa-camera',
                'is_active' => true,
                'estimated_duration' => 30
            ],
            [
                'id' => 3,
                'name' => 'Évaluation des dommages',
                'description' => 'Diagnostic et évaluation des réparations nécessaires',
                'order' => 3,
                'color' => 'warning',
                'icon' => 'fas fa-search',
                'is_active' => true,
                'estimated_duration' => 60
            ],
            [
                'id' => 4,
                'name' => 'Commande des pièces',
                'description' => 'Commande des pièces détachées nécessaires',
                'order' => 4,
                'color' => 'primary',
                'icon' => 'fas fa-shopping-cart',
                'is_active' => true,
                'estimated_duration' => 30
            ],
            [
                'id' => 5,
                'name' => 'Réparation',
                'description' => 'Intervention et réparation du véhicule',
                'order' => 5,
                'color' => 'danger',
                'icon' => 'fas fa-wrench',
                'is_active' => true,
                'estimated_duration' => 240
            ],
            [
                'id' => 6,
                'name' => 'Contrôle qualité',
                'description' => 'Vérification de la qualité des réparations',
                'order' => 6,
                'color' => 'success',
                'icon' => 'fas fa-check-circle',
                'is_active' => true,
                'estimated_duration' => 45
            ],
            [
                'id' => 7,
                'name' => 'Nettoyage',
                'description' => 'Nettoyage du véhicule après réparation',
                'order' => 7,
                'color' => 'info',
                'icon' => 'fas fa-spray-can',
                'is_active' => true,
                'estimated_duration' => 30
            ],
            [
                'id' => 8,
                'name' => 'Photos après réparation',
                'description' => 'Prise de photos après intervention',
                'order' => 8,
                'color' => 'success',
                'icon' => 'fas fa-camera',
                'is_active' => true,
                'estimated_duration' => 20
            ],
            [
                'id' => 9,
                'name' => 'Facturation',
                'description' => 'Établissement de la facture',
                'order' => 9,
                'color' => 'warning',
                'icon' => 'fas fa-file-invoice',
                'is_active' => true,
                'estimated_duration' => 15
            ],
            [
                'id' => 10,
                'name' => 'Livraison',
                'description' => 'Remise du véhicule au client',
                'order' => 10,
                'color' => 'success',
                'icon' => 'fas fa-handshake',
                'is_active' => true,
                'estimated_duration' => 15
            ]
        ];

        foreach ($stages as $stage) {
            DB::table('stages')->updateOrInsert(
                ['id' => $stage['id']], 
                $stage
            );
        }
    }
}
