"use strict";(function(){function c(f,e){return f.className.split(" ").indexOf(e)!==-1}function d(f,e){f=f.replace(/\r/g,"").replace(/\t/g,"  ").replace(/^ *\n+/,"\n").replace(/[\s\n]+$/,"");f=f.replace(new RegExp("\\n"+f.match(/^\n( *)/)[1],"g"),"\n");if(e){f=f.replace(/class="([^"]+)"/g,function(h,k){var o=k.replace(/^\s+|\s+$/,"").replace(/\s+/g," ").split(" ");for(var n=0,j=e.length,g;n<j;n++){if((g=o.indexOf(e[n]))!==-1){o.splice(g,1)}}return'class="'+o.join(" ")+'"'})}return f.replace(/\s+class=""/ig,"").replace(/([a-z]+)=""/ig,"$1").replace(/javascript:void\(0\)/g,"#").replace(/^\n/,"")}function b(e,f){return new ClipboardJS(e,{text:function(){return f}})}function a(j,k){document.querySelector(".datta-example-modal-content").innerHTML='<pre><code class="hljs html xml">'+k+"</code></pre>";var e=document.querySelector(".md-datta-example-modal-copy");var i=document.querySelector(".datta-example-modal-close");var f=null;var g=b(e,j);g.on("success",function(l){if(f){clearTimeout(f);f=null}e.className=e.className.replace(" copied","");e.className+=" copied";f=setTimeout(function(){e.className=e.className.replace(" copied","")},1000)});var h=function(){document.querySelector(".datta-example-modal-content").innerHTML="";document.querySelector(".datta-example-modal").scrollTop=0;i.removeEventListener("click",h);g.destroy();document.documentElement.className=document.documentElement.className.replace(" datta-example-modal-opened","")};i.addEventListener("click",h);document.documentElement.className+=" datta-example-modal-opened"}Array.prototype.slice.call(document.querySelectorAll(".datta-example")).forEach(function(i){var l=document.createElement("div");l.className="datta-example-btns";var m=document.createElement("a");m.href="javascript:void(0)";m.className="datta-example-btn copy";var k=document.createElement("a");k.href="javascript:void(0)";k.className="datta-example-btn";k.innerHTML="SOURCE";l.appendChild(m);l.appendChild(k);var h=(i.getAttribute("data-blacklist")||null);var g=(h&&h.split(","))||null;var e=d(i.innerHTML,g);var j=hljs.highlight("html",e).value;i.appendChild(l);var f=null;b(m,e).on("success",function(n){if(f){clearTimeout(f);f=null}m.className=m.className.replace(" copied","");m.className+=" copied";f=setTimeout(function(){m.className=m.className.replace(" copied","")},1000)});k.addEventListener("click",function(n){a(e,j)})})})();