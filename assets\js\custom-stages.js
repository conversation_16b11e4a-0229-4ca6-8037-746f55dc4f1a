// JavaScript personnalisé pour la gestion des étapes
document.addEventListener('DOMContentLoaded', function() {
    // Vérifier si nous sommes sur la page atelier.php
    if (window.location.href.includes('/atelier')) {
        console.log('Page atelier.php détectée');

        // Fonction pour mettre à jour la barre de progression
        function updateProgressBar() {
            console.log('Mise à jour de la barre de progression');

            // Vérifier si la barre de progression existe
            const progressbar = document.querySelector('.progressbar');
            if (!progressbar) {
                console.error('Barre de progression non trouvée');
                return;
            }

            console.log('Barre de progression trouvée');

            // Définir les 10 étapes avec des noms plus courts pour un meilleur affichage
            // Changement: Livraison est maintenant la dernière étape (10) et Facturation est l'étape 9
            const allStages = [
                { id: 1, name: '<PERSON><PERSON><PERSON>vous', order_num: 1 },
                { id: 2, name: '<PERSON>s avant', order_num: 2 },
                { id: 3, name: '<PERSON><PERSON><PERSON><PERSON>', order_num: 3 },
                { id: 4, name: '<PERSON><PERSON><PERSON>', order_num: 4 },
                { id: 5, name: 'Réparation', order_num: 5 },
                { id: 6, name: 'Contrôle', order_num: 6 },
                { id: 7, name: 'Nettoyage', order_num: 7 },
                { id: 8, name: 'Photos après', order_num: 8 },
                { id: 9, name: 'Facturation', order_num: 9 },
                { id: 10, name: 'Livraison', order_num: 10 }
            ];

            // Vider la barre de progression
            progressbar.innerHTML = '';

            // Récupérer l'étape actuelle
            const modalBody = document.getElementById('missionDetailsModalBody');

            // Essayer de récupérer les données des étapes à partir de l'attribut data-stages
            let currentStageId = 2;
            let currentStageOrder = 2;

            try {
                const stagesDataJson = modalBody.getAttribute('data-stages');
                if (stagesDataJson) {
                    const stagesData = JSON.parse(stagesDataJson);
                    currentStageId = parseInt(stagesData.current_stage_id || '2');
                    currentStageOrder = parseInt(stagesData.current_stage_order || '2');
                } else {
                    // Fallback: utiliser l'attribut data-current-stage-id
                    currentStageId = parseInt(modalBody.getAttribute('data-current-stage-id') || '2');
                    currentStageOrder = currentStageId;
                }
            } catch (e) {
                console.error('Erreur lors de la récupération des données des étapes:', e);
                // Fallback: utiliser l'attribut data-current-stage-id
                currentStageId = parseInt(modalBody.getAttribute('data-current-stage-id') || '2');
                currentStageOrder = currentStageId;
            }

            console.log('Étape actuelle:', currentStageId, 'Ordre:', currentStageOrder);

            const isLastStage = currentStageId === 10;

            // Ajouter toutes les étapes à la barre de progression
            allStages.forEach(stage => {
                let stageClass = '';
                let stageStatusBadge = '';

                // Déterminer la classe CSS pour l'étape
                if (isLastStage || stage.order_num < currentStageOrder) {
                    stageClass = 'completed'; // Étape terminée
                    stageStatusBadge = '<span class="stage-badge completed"><i class="fas fa-check"></i></span>';
                } else if (stage.order_num === currentStageOrder) {
                    stageClass = 'active current'; // Étape actuelle
                    stageStatusBadge = '<span class="stage-badge current"><i class="fas fa-cog fa-spin"></i></span>';
                } else {
                    stageClass = 'pending'; // Étape à venir
                    stageStatusBadge = '<span class="stage-badge pending"><i class="fas fa-clock"></i></span>';
                }

                // Créer l'élément de l'étape
                const li = document.createElement('li');
                li.className = stageClass;
                li.innerHTML = `${stage.name}`; // Supprimer le badge pour un affichage plus propre

                // Ajouter l'étape à la barre de progression
                progressbar.appendChild(li);
            });

            console.log('Étapes ajoutées à la barre de progression');
        }

        // Observer les mutations du DOM pour détecter quand le modal est chargé
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Vérifier si la barre de progression a été ajoutée
                    const progressbar = document.querySelector('.progressbar');
                    if (progressbar) {
                        console.log('Barre de progression détectée, mise à jour...');
                        // Attendre un peu pour s'assurer que tout est chargé
                        setTimeout(updateProgressBar, 100);
                        // Arrêter l'observation une fois que la barre de progression a été mise à jour
                        observer.disconnect();
                    }
                }
            });
        });

        // Observer le corps du modal
        const modalBody = document.getElementById('missionDetailsModalBody');
        if (modalBody) {
            observer.observe(modalBody, { childList: true, subtree: true });
        }

        // Attendre que le modal soit chargé
        $(document).on('shown.bs.modal', '#missionDetailsModal', function() {
            console.log('Modal affiché, vérification des étapes');
            // Attendre un peu pour s'assurer que tout est chargé
            setTimeout(updateProgressBar, 100);
        });
    }
});
