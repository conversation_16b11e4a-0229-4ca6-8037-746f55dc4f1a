<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class DamageAssessment extends Model
{
    use HasFactory;

    protected $table = 'damage_assessments';
    protected $guarded = [];

    protected $fillable = [
        'rdv_id',
        'damage_type',
        'damage_description',
        'severity_level',
        'repair_method',
        'estimated_cost',
        'estimated_time',
        'photos',
        'notes',
        'assessed_by',
        'assessment_date'
    ];

    protected $casts = [
        'estimated_cost' => 'decimal:2',
        'estimated_time' => 'integer',
        'photos' => 'array',
        'assessment_date' => 'datetime'
    ];

    /**
     * Relation avec le rendez-vous
     */
    public function rdv()
    {
        return $this->belongsTo(Rdv::class, 'rdv_id');
    }

    /**
     * Relation avec l'évaluateur
     */
    public function assessedBy()
    {
        return $this->belongsTo(User::class, 'assessed_by');
    }

    /**
     * Relation avec les pièces requises
     */
    public function requiredParts()
    {
        return $this->hasMany(RequiredPart::class, 'damage_assessment_id');
    }

    /**
     * Obtenir le niveau de sévérité formaté
     */
    public function getFormattedSeverityAttribute()
    {
        $severities = [
            'low' => 'Faible',
            'medium' => 'Moyen',
            'high' => 'Élevé',
            'critical' => 'Critique'
        ];

        return $severities[$this->severity_level] ?? $this->severity_level;
    }

    /**
     * Obtenir le temps estimé formaté
     */
    public function getFormattedTimeAttribute()
    {
        if (!$this->estimated_time) {
            return 'Non défini';
        }

        $hours = floor($this->estimated_time / 60);
        $minutes = $this->estimated_time % 60;

        if ($hours > 0) {
            return $hours . 'h ' . $minutes . 'min';
        }

        return $minutes . 'min';
    }

    /**
     * Scope pour les évaluations par niveau de sévérité
     */
    public function scopeBySeverity($query, $severity)
    {
        return $query->where('severity_level', $severity);
    }

    /**
     * Scope pour les évaluations critiques
     */
    public function scopeCritical($query)
    {
        return $query->where('severity_level', 'critical');
    }
}
