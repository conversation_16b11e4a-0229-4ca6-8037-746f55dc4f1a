<?php

use App\Http\Controllers\AtelierController;
use Illuminate\Support\Facades\Route;

use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\StatsController;
use App\Http\Controllers\RdvController;




Route::get('/', function () {
    return view('welcome');
})->middleware('auth');

Route::get('/atelier', function () {
    return view('atelier');
})->middleware('auth');

Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('/login', [LoginController::class, 'login'])->name('login.submit');
Route::get('/rdv-stats', [StatsController::class, 'getRdvStats']);
Route::get('/rdv-etat', [RdvController::class, 'getRdvStats']);




// تسجيل الخروج
Route::post('/logout', [LoginController::class, 'logout'])->name('logout');


