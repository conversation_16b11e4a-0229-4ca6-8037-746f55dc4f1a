<?php

use App\Http\Controllers\AtelierController;
use App\Http\Controllers\ClientController;
use App\Http\Controllers\CalendrierController;
use App\Http\Controllers\KpiController;
use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\StatsController;
use App\Http\Controllers\RdvController;

// Routes publiques
Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('/login', [LoginController::class, 'login'])->name('login.submit');

// Routes protégées par authentification
Route::middleware('auth')->group(function () {
    // Page d'accueil
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');
    Route::get('/', function () {
        return view('dashboard');
    })->name('dashboard');

    // Atelier
    Route::get('/atelier', [AtelierController::class, 'index'])->name('workshop.index');
    Route::get('/atelier/{id}/details', [AtelierController::class, 'details'])->name('workshop.details');
    Route::get('/atelier/{id}/manage', [AtelierController::class, 'manage'])->name('workshop.manage');
    Route::post('/atelier/upload-photos', [AtelierController::class, 'uploadPhotos'])->name('workshop.uploadPhotos');
    Route::post('/atelier/complete-stage', [AtelierController::class, 'completeStage'])->name('workshop.completeStage');
    Route::post('/atelier/return-to-repair', [AtelierController::class, 'returnToRepair'])->name('workshop.returnToRepair');

    // Clients
    Route::resource('clients', ClientController::class);
    Route::post('/clients/{client}/toggle-status', [ClientController::class, 'toggleStatus'])->name('clients.toggleStatus');
    Route::get('/clients-search', [ClientController::class, 'search'])->name('clients.search');

    // Calendrier
    Route::get('/calendrier', [CalendrierController::class, 'index'])->name('calendrier.index');
    Route::get('/calendrier/events', [CalendrierController::class, 'getEvents'])->name('calendrier.events');
    Route::get('/calendrier/slots', [CalendrierController::class, 'getAvailableSlots'])->name('calendrier.slots');
    Route::post('/calendrier', [CalendrierController::class, 'store'])->name('calendrier.store');
    Route::put('/calendrier/{rdv}', [CalendrierController::class, 'update'])->name('calendrier.update');
    Route::delete('/calendrier/{rdv}', [CalendrierController::class, 'destroy'])->name('calendrier.destroy');

    // KPI
    Route::get('/kpi', [KpiController::class, 'index'])->name('kpi.index');
    Route::get('/kpi/export', [KpiController::class, 'exportPdf'])->name('kpi.export');
    Route::get('/kpi/chart-data', [KpiController::class, 'getChartData'])->name('kpi.chartData');

    // Profil
    Route::get('/profile', [ProfileController::class, 'index'])->name('profile.index');
    Route::post('/profile', [ProfileController::class, 'updateProfile'])->name('profile.update');
    Route::post('/profile/password', [ProfileController::class, 'updatePassword'])->name('profile.updatePassword');
    Route::post('/profile/avatar', [ProfileController::class, 'updateAvatar'])->name('profile.updateAvatar');
    Route::delete('/profile/avatar', [ProfileController::class, 'deleteAvatar'])->name('profile.deleteAvatar');
    Route::post('/profile/notifications', [ProfileController::class, 'updateNotifications'])->name('profile.updateNotifications');
    Route::get('/profile/activity', [ProfileController::class, 'getActivity'])->name('profile.activity');
    Route::get('/profile/export', [ProfileController::class, 'exportData'])->name('profile.exportData');

    // API pour les statistiques
    Route::get('/rdv-stats', [StatsController::class, 'getRdvStats']);
    Route::get('/rdv-etat', [RdvController::class, 'getRdvStats']);

    // Déconnexion
    Route::post('/logout', [LoginController::class, 'logout'])->name('logout');
    Route::get('/logout', [LoginController::class, 'logout'])->name('logout.get');
});


