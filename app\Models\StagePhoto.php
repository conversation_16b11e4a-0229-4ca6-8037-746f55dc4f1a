<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class StagePhoto extends Model
{
    protected $table = 'stage_photos';
    protected $guarded = [];

    public function rdv()
    {
        return $this->belongsTo(Rdv::class, 'rdv_id');
    }

    public function stage()
    {
        return $this->belongsTo(Stage::class, 'stage_id');
    }

    public function uploadedBy()
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }
}